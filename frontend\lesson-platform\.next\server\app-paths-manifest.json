{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/student-data/route": "app/api/student-data/route.js", "/api/enhance-content/route": "app/api/enhance-content/route.js", "/dashboard/page": "app/dashboard/page.js", "/start-lesson/page": "app/start-lesson/page.js", "/login/page": "app/login/page.js", "/classroom/page": "app/classroom/page.js", "/student-dashboard/[id]/page": "app/student-dashboard/[id]/page.js"}