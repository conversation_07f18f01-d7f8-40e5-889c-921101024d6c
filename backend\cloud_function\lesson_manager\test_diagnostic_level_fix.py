#!/usr/bin/env python3
"""
Test script to verify the diagnostic level assignment fix.

This script tests the validate_and_correct_diagnostic_level_assignment function
with various student answer scenarios to ensure correct level assignment.

Usage:
    python test_diagnostic_level_fix.py
"""

import sys
import os

# Add the lesson manager directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the validation function
try:
    from main import validate_and_correct_diagnostic_level_assignment
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the lesson_manager directory")
    sys.exit(1)

def test_diagnostic_scoring():
    """Test various diagnostic scoring scenarios"""
    
    print("🧪 Testing Diagnostic Level Assignment Fix")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Poor Performance (mostly 'I don't know')",
            "answers": {
                'q1': 'all of it',
                'q2': "I don't know", 
                'q3': 'Yes it does',
                'q4': "I don't know",
                'q5': "I don't know"
            },
            "current_level": 5,
            "ai_assigned": 6,
            "expected": 4,  # Should move DOWN
            "description": "Student gave mostly 'I don't know' responses - should move down"
        },
        {
            "name": "Excellent Performance (detailed answers)",
            "answers": {
                'q1': 'Entrepreneurship involves identifying business opportunities and creating value through innovation',
                'q2': 'Market research helps understand customer needs and validate business ideas before investment',
                'q3': 'A business plan outlines goals, strategies, financial projections and operational details',
                'q4': 'Target customers are specific groups who would benefit most from your product or service',
                'q5': 'Profit is revenue minus expenses, representing the financial return on business investment'
            },
            "current_level": 5,
            "ai_assigned": 5,
            "expected": 6,  # Should move UP
            "description": "Student gave detailed, thoughtful answers - should move up"
        },
        {
            "name": "Good Performance (4 substantial answers)",
            "answers": {
                'q1': 'Starting and running a business to make money',
                'q2': 'To understand what customers want and need',
                'q3': 'A document that explains your business goals and how to achieve them',
                'q4': 'People who buy your products or services',
                'q5': "I'm not sure about this one"
            },
            "current_level": 5,
            "ai_assigned": 6,
            "expected": 5,  # Should stay SAME
            "description": "Student gave 4 substantial answers - should stay at same level"
        },
        {
            "name": "Fair Performance (2-3 substantial answers)",
            "answers": {
                'q1': 'Business stuff',
                'q2': 'To know about customers and what they like',
                'q3': 'A plan for your business',
                'q4': "I don't know",
                'q5': "Not sure"
            },
            "current_level": 5,
            "ai_assigned": 4,
            "expected": 5,  # Should stay SAME
            "description": "Student gave 2-3 substantial answers - should stay at same level"
        },
        {
            "name": "Very Poor Performance (all short/don't know)",
            "answers": {
                'q1': 'No',
                'q2': "I don't know",
                'q3': "Not sure",
                'q4': "IDK",
                'q5': "Maybe"
            },
            "current_level": 5,
            "ai_assigned": 5,
            "expected": 4,  # Should move DOWN
            "description": "Student gave no substantial answers - should move down"
        },
        {
            "name": "Edge Case - Level 1 (can't go lower)",
            "answers": {
                'q1': "I don't know",
                'q2': "I don't know",
                'q3': "I don't know",
                'q4': "I don't know",
                'q5': "I don't know"
            },
            "current_level": 1,
            "ai_assigned": 2,
            "expected": 1,  # Should stay at minimum level
            "description": "At minimum level, can't go lower"
        },
        {
            "name": "Edge Case - Level 10 (can't go higher)",
            "answers": {
                'q1': 'Comprehensive understanding of entrepreneurial ecosystems and venture capital dynamics',
                'q2': 'Advanced market segmentation using demographic, psychographic and behavioral analysis',
                'q3': 'Strategic business planning with financial modeling and risk assessment frameworks',
                'q4': 'Customer persona development using data-driven insights and market research methodologies',
                'q5': 'Profit optimization through cost management, pricing strategies and revenue diversification'
            },
            "current_level": 10,
            "ai_assigned": 10,
            "expected": 10,  # Should stay at maximum level
            "description": "At maximum level, can't go higher"
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print(f"Current Level: {test_case['current_level']}")
        print(f"AI Assigned: {test_case['ai_assigned']}")
        print(f"Expected: {test_case['expected']}")
        
        # Run the validation function
        result = validate_and_correct_diagnostic_level_assignment(
            test_case['answers'],
            test_case['current_level'],
            test_case['ai_assigned'],
            f"test_{i}"
        )
        
        # Check if result matches expected
        if result == test_case['expected']:
            print(f"✅ PASS: Got {result} (expected {test_case['expected']})")
            passed_tests += 1
        else:
            print(f"❌ FAIL: Got {result} (expected {test_case['expected']})")
            
        # Show student answers for context
        print("Student Answers:")
        for q, a in test_case['answers'].items():
            print(f"  {q}: '{a}'")
    
    print(f"\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Diagnostic level assignment fix is working correctly.")
    else:
        print(f"⚠️ {total_tests - passed_tests} tests failed. Please review the implementation.")
    
    print("=" * 60)
    
    return passed_tests == total_tests

def test_original_failing_case():
    """Test the specific case from the user's log that was failing"""
    
    print(f"\n🔍 Testing Original Failing Case from User Log")
    print("=" * 60)
    
    # The exact answers from the user's log
    failing_answers = {
        'q4': "I don't know",
        'q1': 'all of it', 
        'q2': "I don't know",
        'q3': 'Yes it does',
        'q5': "I don't know"
    }
    
    current_level = 5  # From log: current_probing_level_number = 5
    ai_assigned = 6    # From log: assigned_level_for_teaching = 6 (WRONG!)
    expected = 4       # Should be 4 (move down due to poor performance)
    
    print(f"Original Case:")
    print(f"  Current Level: {current_level}")
    print(f"  AI Assigned: {ai_assigned} (INCORRECT)")
    print(f"  Expected: {expected} (move DOWN due to poor answers)")
    print(f"  Student Answers: {failing_answers}")
    
    result = validate_and_correct_diagnostic_level_assignment(
        failing_answers, current_level, ai_assigned, "original_case"
    )
    
    if result == expected:
        print(f"✅ FIXED: Validation function correctly assigned level {result}")
        print(f"   The AI's incorrect assignment of {ai_assigned} would be overridden")
        return True
    else:
        print(f"❌ STILL BROKEN: Got {result}, expected {expected}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Diagnostic Level Assignment Fix Tests...")
    
    # Test the general scoring logic
    general_tests_passed = test_diagnostic_scoring()
    
    # Test the specific failing case
    original_case_fixed = test_original_failing_case()
    
    print(f"\n🏁 FINAL RESULTS:")
    print(f"   General Tests: {'✅ PASSED' if general_tests_passed else '❌ FAILED'}")
    print(f"   Original Case: {'✅ FIXED' if original_case_fixed else '❌ STILL BROKEN'}")
    
    if general_tests_passed and original_case_fixed:
        print(f"\n🎉 SUCCESS! The diagnostic level assignment fix is working correctly.")
        print(f"   Students with poor answers will now be assigned appropriate lower levels.")
    else:
        print(f"\n⚠️ Issues remain. Please review the implementation.")
