#!/usr/bin/env python3
"""
Curriculum Integration Test
Verifies that the enhanced teaching level personalization system properly integrates 
gold standard curriculum data for standardized, curriculum-aligned instruction delivery.
"""

import json
import re
from datetime import datetime, timezone

def test_curriculum_data_integration():
    """Test curriculum data integration and template parameter population."""
    print("🧪 TESTING CURRICULUM DATA INTEGRATION")
    print("=" * 80)
    
    # Mock gold standard curriculum data structure
    mock_curriculum_data = {
        "levels": {
            "1": {
                "description": "Foundational Level 1: Introduction to basic mathematical concepts with concrete manipulatives",
                "learning_objectives": [
                    "Count objects up to 20",
                    "Recognize basic shapes",
                    "Understand simple addition and subtraction"
                ],
                "key_concepts": ["counting", "shapes", "basic operations"],
                "keywords": ["numbers", "add", "subtract", "circle", "square"],
                "assessment_criteria": [
                    "Can count objects accurately",
                    "Identifies basic shapes correctly",
                    "Solves simple addition problems"
                ],
                "content_suggestions": [
                    "Use physical manipulatives",
                    "Provide visual examples",
                    "Practice with concrete objects"
                ],
                "title": "Basic Number Concepts"
            },
            "5": {
                "description": "Intermediate Level 5: Application of mathematical concepts to solve real-world problems",
                "learning_objectives": [
                    "Solve multi-step word problems",
                    "Apply fractions in practical contexts",
                    "Understand basic geometry principles"
                ],
                "key_concepts": ["problem-solving", "fractions", "geometry", "measurement"],
                "keywords": ["calculate", "measure", "fraction", "area", "perimeter"],
                "assessment_criteria": [
                    "Solves word problems with correct strategy",
                    "Applies fraction concepts accurately",
                    "Demonstrates geometric understanding"
                ],
                "content_suggestions": [
                    "Use real-world scenarios",
                    "Encourage multiple solution methods",
                    "Connect to practical applications"
                ],
                "title": "Applied Mathematical Thinking"
            },
            "9": {
                "description": "Advanced Level 9: Synthesis and evaluation of complex mathematical relationships",
                "learning_objectives": [
                    "Analyze complex mathematical patterns",
                    "Evaluate multiple solution strategies",
                    "Create original mathematical models"
                ],
                "key_concepts": ["analysis", "synthesis", "modeling", "proof", "abstraction"],
                "keywords": ["analyze", "prove", "model", "abstract", "generalize"],
                "assessment_criteria": [
                    "Demonstrates analytical thinking",
                    "Evaluates solution effectiveness",
                    "Creates valid mathematical arguments"
                ],
                "content_suggestions": [
                    "Present open-ended challenges",
                    "Encourage mathematical reasoning",
                    "Foster creative problem-solving"
                ],
                "title": "Mathematical Analysis and Modeling"
            }
        }
    }
    
    # Test scenarios for different curriculum integration cases
    test_scenarios = [
        {
            "name": "Curriculum Data Available",
            "teaching_level": 5,
            "gold_standard_data": mock_curriculum_data,
            "expected_source": "gold_standard_curriculum",
            "expected_description": "Intermediate Level 5: Application of mathematical concepts",
            "expected_objectives_count": 3,
            "expected_concepts_count": 4
        },
        {
            "name": "Level Not Found in Curriculum",
            "teaching_level": 7,
            "gold_standard_data": mock_curriculum_data,
            "expected_source": "level_not_found_fallback",
            "expected_description": "Advanced Level 7: Analyzing and evaluating",
            "expected_objectives_count": 0,  # Generic fallback
            "expected_concepts_count": 0     # Generic fallback
        },
        {
            "name": "No Curriculum Data Available",
            "teaching_level": 3,
            "gold_standard_data": None,
            "expected_source": "no_gold_standard_data_fallback",
            "expected_description": "Developing Level 3: Strengthening core concepts",
            "expected_objectives_count": 0,  # Generic fallback
            "expected_concepts_count": 0     # Generic fallback
        }
    ]
    
    # Test each scenario
    for scenario in test_scenarios:
        print(f"\n🎯 TESTING: {scenario['name']}")
        print("-" * 60)
        
        # Simulate curriculum data extraction
        curriculum_data_source = "unknown"
        gs_level_description = ""
        gs_level_objectives = ""
        gs_level_key_concepts = ""
        
        teaching_level = scenario['teaching_level']
        gold_standard_data = scenario['gold_standard_data']
        
        # Simulate the curriculum integration logic
        if gold_standard_data and teaching_level:
            levels_data = gold_standard_data.get("levels", {})
            level_key = str(teaching_level)
            
            if level_key in levels_data:
                level_data = levels_data[level_key]
                curriculum_data_source = "gold_standard_curriculum"
                
                gs_level_description = level_data.get("description", f"Level {teaching_level} curriculum-aligned instruction")
                
                learning_objectives = level_data.get("learning_objectives", [])
                gs_level_objectives = "; ".join(learning_objectives) if learning_objectives else f"Standard Level {teaching_level} learning objectives"
                
                key_concepts = level_data.get("key_concepts", [])
                if not key_concepts:
                    key_concepts = level_data.get("keywords", [])
                gs_level_key_concepts = "; ".join(key_concepts) if key_concepts else f"Core concepts for Level {teaching_level}"
                
            else:
                curriculum_data_source = "level_not_found_fallback"
                gs_level_description = get_mock_generic_description(teaching_level)
                gs_level_objectives = get_mock_generic_objectives(teaching_level)
                gs_level_key_concepts = get_mock_generic_concepts(teaching_level)
        else:
            curriculum_data_source = "no_gold_standard_data_fallback"
            gs_level_description = get_mock_generic_description(teaching_level)
            gs_level_objectives = get_mock_generic_objectives(teaching_level)
            gs_level_key_concepts = get_mock_generic_concepts(teaching_level)
        
        # Verify results
        print(f"📊 Results:")
        print(f"   Data Source: {curriculum_data_source}")
        print(f"   Description: {gs_level_description[:80]}...")
        print(f"   Objectives: {gs_level_objectives[:80]}...")
        print(f"   Key Concepts: {gs_level_key_concepts[:80]}...")
        
        # Check expectations
        source_match = curriculum_data_source == scenario['expected_source']
        description_match = scenario['expected_description'].lower() in gs_level_description.lower()
        
        print(f"✅ Source Match: {'✓' if source_match else '✗'} (Expected: {scenario['expected_source']})")
        print(f"✅ Description Match: {'✓' if description_match else '✗'}")
        
        if source_match and description_match:
            print(f"🎉 SUCCESS: {scenario['name']} working correctly")
        else:
            print(f"❌ FAILURE: {scenario['name']} needs attention")

def get_mock_generic_description(level: int) -> str:
    """Mock generic level descriptions for testing."""
    descriptions = {
        1: "Foundational Level 1: Introduction to basic concepts",
        2: "Basic Level 2: Building fundamental understanding",
        3: "Developing Level 3: Strengthening core concepts",
        4: "Intermediate Level 4: Applying knowledge to solve problems",
        5: "Standard Level 5: Demonstrating competency",
        6: "Proficient Level 6: Mastering concepts",
        7: "Advanced Level 7: Analyzing and evaluating",
        8: "Expert Level 8: Synthesizing knowledge",
        9: "Mastery Level 9: Creating original solutions",
        10: "Excellence Level 10: Demonstrating exceptional understanding"
    }
    return descriptions.get(level, f"Level {level}: Appropriate cognitive challenge")

def get_mock_generic_objectives(level: int) -> str:
    """Mock generic learning objectives for testing."""
    if level <= 3:
        return "Understand basic concepts; Identify key terms; Apply simple procedures"
    elif level <= 6:
        return "Analyze problems; Apply concepts to new situations; Compare and contrast ideas"
    else:
        return "Evaluate complex scenarios; Synthesize knowledge; Create original solutions"

def get_mock_generic_concepts(level: int) -> str:
    """Mock generic key concepts for testing."""
    if level <= 3:
        return "Basic terminology; Fundamental principles; Simple applications"
    elif level <= 6:
        return "Intermediate concepts; Problem-solving strategies; Real-world applications"
    else:
        return "Advanced theories; Complex problem-solving; Critical analysis"

def test_template_parameter_usage():
    """Test that curriculum parameters are properly used in templates."""
    print("\n🔍 TESTING TEMPLATE PARAMETER USAGE")
    print("=" * 80)
    
    # Check for curriculum-specific parameters in template
    curriculum_parameters = [
        'gs_level_description_for_teaching',
        'gs_level_objectives_for_teaching',
        'gs_level_key_concepts_for_teaching',
        'gs_level_assessment_criteria_for_teaching',
        'gs_level_content_suggestions_for_teaching'
    ]
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find BASE_INSTRUCTOR_RULES template
        template_start = content.find('BASE_INSTRUCTOR_RULES = """')
        template_end = content.find('"""', template_start + 30)
        
        if template_start != -1 and template_end != -1:
            template_content = content[template_start:template_end]
            
            found_parameters = []
            for param in curriculum_parameters:
                if f'{{{param}}}' in template_content:
                    found_parameters.append(param)
            
            print(f"📋 Template Analysis:")
            print(f"   Template length: {len(template_content)} characters")
            print(f"   Curriculum parameters found: {len(found_parameters)}/{len(curriculum_parameters)}")
            print(f"   Found parameters: {found_parameters}")
            
            # Check for curriculum-specific instructions
            curriculum_instructions = [
                'CURRICULUM-ALIGNED',
                'CURRICULUM LEVEL DEFINITION',
                'CURRICULUM LEARNING OBJECTIVES',
                'CURRICULUM KEY CONCEPTS',
                'CURRICULUM ASSESSMENT CRITERIA'
            ]
            
            found_instructions = []
            for instruction in curriculum_instructions:
                if instruction in template_content:
                    found_instructions.append(instruction)
            
            print(f"   Curriculum instructions found: {len(found_instructions)}/{len(curriculum_instructions)}")
            print(f"   Found instructions: {found_instructions}")
            
            if len(found_parameters) >= 4 and len(found_instructions) >= 3:
                print("✅ SUCCESS: Template includes comprehensive curriculum integration")
            elif len(found_parameters) >= 2:
                print("⚠️ PARTIAL: Template includes some curriculum integration")
            else:
                print("❌ FAILURE: Template lacks curriculum integration")
                
        else:
            print("❌ ERROR: Could not find BASE_INSTRUCTOR_RULES template")
            
    except Exception as e:
        print(f"❌ ERROR: Could not analyze template: {e}")

def test_fallback_mechanisms():
    """Test fallback mechanisms for curriculum data failures."""
    print("\n🛡️ TESTING FALLBACK MECHANISMS")
    print("=" * 80)
    
    fallback_scenarios = [
        {
            "name": "Malformed Curriculum Data",
            "data": {"levels": {"5": {"description": None, "learning_objectives": "not_a_list"}}},
            "level": 5,
            "expected_fallback": True
        },
        {
            "name": "Empty Curriculum Data",
            "data": {"levels": {}},
            "level": 3,
            "expected_fallback": True
        },
        {
            "name": "Missing Required Fields",
            "data": {"levels": {"2": {"title": "Test Level"}}},  # Missing description, objectives, etc.
            "level": 2,
            "expected_fallback": True
        }
    ]
    
    for scenario in fallback_scenarios:
        print(f"\n🧪 Testing: {scenario['name']}")
        print("-" * 40)
        
        # Simulate error handling
        try:
            data = scenario['data']
            level = scenario['level']
            level_data = data.get("levels", {}).get(str(level), {})
            
            # Test description extraction
            description = level_data.get("description")
            if not description or not isinstance(description, str):
                description = get_mock_generic_description(level)
                fallback_used = True
            else:
                fallback_used = False
            
            # Test objectives extraction
            objectives = level_data.get("learning_objectives", [])
            if not objectives or not isinstance(objectives, list):
                objectives = get_mock_generic_objectives(level)
                fallback_used = True
            
            print(f"   Fallback Used: {'✓' if fallback_used else '✗'}")
            print(f"   Description: {description[:50]}...")
            print(f"   Expected Fallback: {'✓' if scenario['expected_fallback'] else '✗'}")
            
            if fallback_used == scenario['expected_fallback']:
                print(f"   🎉 SUCCESS: Fallback mechanism working correctly")
            else:
                print(f"   ❌ FAILURE: Fallback mechanism not working as expected")
                
        except Exception as e:
            print(f"   ⚠️ Exception handled: {e}")
            print(f"   🎉 SUCCESS: Error handling working (fallback triggered)")

def main():
    """Run all curriculum integration tests."""
    print("🚀 CURRICULUM INTEGRATION VERIFICATION")
    print("=" * 80)
    print(f"Test started at: {datetime.now(timezone.utc).isoformat()}")
    
    # Run all tests
    test_curriculum_data_integration()
    test_template_parameter_usage()
    test_fallback_mechanisms()
    
    print("\n" + "=" * 80)
    print("🏁 CURRICULUM INTEGRATION TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    main()
