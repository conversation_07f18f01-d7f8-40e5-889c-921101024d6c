"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mime-types";
exports.ids = ["vendor-chunks/mime-types"];
exports.modules = {

/***/ "(rsc)/./node_modules/mime-types/index.js":
/*!******************************************!*\
  !*** ./node_modules/mime-types/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*!\r\n * mime-types\r\n * Copyright(c) 2014 Jonathan Ong\r\n * Copyright(c) 2015 Douglas Christopher Wilson\r\n * MIT Licensed\r\n */\r\n\r\n\r\n\r\n/**\r\n * Module dependencies.\r\n * @private\r\n */\r\n\r\nvar db = __webpack_require__(/*! mime-db */ \"(rsc)/./node_modules/mime-db/index.js\")\r\nvar extname = (__webpack_require__(/*! path */ \"path\").extname)\r\n\r\n/**\r\n * Module variables.\r\n * @private\r\n */\r\n\r\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\r\nvar TEXT_TYPE_REGEXP = /^text\\//i\r\n\r\n/**\r\n * Module exports.\r\n * @public\r\n */\r\n\r\nexports.charset = charset\r\nexports.charsets = { lookup: charset }\r\nexports.contentType = contentType\r\nexports.extension = extension\r\nexports.extensions = Object.create(null)\r\nexports.lookup = lookup\r\nexports.types = Object.create(null)\r\n\r\n// Populate the extensions/types maps\r\npopulateMaps(exports.extensions, exports.types)\r\n\r\n/**\r\n * Get the default charset for a MIME type.\r\n *\r\n * @param {string} type\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction charset (type) {\r\n  if (!type || typeof type !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // TODO: use media-typer\r\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\r\n  var mime = match && db[match[1].toLowerCase()]\r\n\r\n  if (mime && mime.charset) {\r\n    return mime.charset\r\n  }\r\n\r\n  // default text/* to utf-8\r\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\r\n    return 'UTF-8'\r\n  }\r\n\r\n  return false\r\n}\r\n\r\n/**\r\n * Create a full Content-Type header given a MIME type or extension.\r\n *\r\n * @param {string} str\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction contentType (str) {\r\n  // TODO: should this even be in this module?\r\n  if (!str || typeof str !== 'string') {\r\n    return false\r\n  }\r\n\r\n  var mime = str.indexOf('/') === -1\r\n    ? exports.lookup(str)\r\n    : str\r\n\r\n  if (!mime) {\r\n    return false\r\n  }\r\n\r\n  // TODO: use content-type or other module\r\n  if (mime.indexOf('charset') === -1) {\r\n    var charset = exports.charset(mime)\r\n    if (charset) mime += '; charset=' + charset.toLowerCase()\r\n  }\r\n\r\n  return mime\r\n}\r\n\r\n/**\r\n * Get the default extension for a MIME type.\r\n *\r\n * @param {string} type\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction extension (type) {\r\n  if (!type || typeof type !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // TODO: use media-typer\r\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\r\n\r\n  // get extensions\r\n  var exts = match && exports.extensions[match[1].toLowerCase()]\r\n\r\n  if (!exts || !exts.length) {\r\n    return false\r\n  }\r\n\r\n  return exts[0]\r\n}\r\n\r\n/**\r\n * Lookup the MIME type for a file path/extension.\r\n *\r\n * @param {string} path\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction lookup (path) {\r\n  if (!path || typeof path !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // get the extension (\"ext\" or \".ext\" or full path)\r\n  var extension = extname('x.' + path)\r\n    .toLowerCase()\r\n    .substr(1)\r\n\r\n  if (!extension) {\r\n    return false\r\n  }\r\n\r\n  return exports.types[extension] || false\r\n}\r\n\r\n/**\r\n * Populate the extensions and types maps.\r\n * @private\r\n */\r\n\r\nfunction populateMaps (extensions, types) {\r\n  // source preference (least -> most)\r\n  var preference = ['nginx', 'apache', undefined, 'iana']\r\n\r\n  Object.keys(db).forEach(function forEachMimeType (type) {\r\n    var mime = db[type]\r\n    var exts = mime.extensions\r\n\r\n    if (!exts || !exts.length) {\r\n      return\r\n    }\r\n\r\n    // mime -> extensions\r\n    extensions[type] = exts\r\n\r\n    // extension -> mime\r\n    for (var i = 0; i < exts.length; i++) {\r\n      var extension = exts[i]\r\n\r\n      if (types[extension]) {\r\n        var from = preference.indexOf(db[types[extension]].source)\r\n        var to = preference.indexOf(mime.source)\r\n\r\n        if (types[extension] !== 'application/octet-stream' &&\r\n          (from > to || (from === to && types[extension].substr(0, 12) === 'application/'))) {\r\n          // skip the remapping\r\n          continue\r\n        }\r\n      }\r\n\r\n      // set the extension -> mime\r\n      types[extension] = type\r\n    }\r\n  })\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mime-types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mime-types/index.js":
/*!******************************************!*\
  !*** ./node_modules/mime-types/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*!\r\n * mime-types\r\n * Copyright(c) 2014 Jonathan Ong\r\n * Copyright(c) 2015 Douglas Christopher Wilson\r\n * MIT Licensed\r\n */\r\n\r\n\r\n\r\n/**\r\n * Module dependencies.\r\n * @private\r\n */\r\n\r\nvar db = __webpack_require__(/*! mime-db */ \"(ssr)/./node_modules/mime-db/index.js\")\r\nvar extname = (__webpack_require__(/*! path */ \"path\").extname)\r\n\r\n/**\r\n * Module variables.\r\n * @private\r\n */\r\n\r\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\r\nvar TEXT_TYPE_REGEXP = /^text\\//i\r\n\r\n/**\r\n * Module exports.\r\n * @public\r\n */\r\n\r\nexports.charset = charset\r\nexports.charsets = { lookup: charset }\r\nexports.contentType = contentType\r\nexports.extension = extension\r\nexports.extensions = Object.create(null)\r\nexports.lookup = lookup\r\nexports.types = Object.create(null)\r\n\r\n// Populate the extensions/types maps\r\npopulateMaps(exports.extensions, exports.types)\r\n\r\n/**\r\n * Get the default charset for a MIME type.\r\n *\r\n * @param {string} type\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction charset (type) {\r\n  if (!type || typeof type !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // TODO: use media-typer\r\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\r\n  var mime = match && db[match[1].toLowerCase()]\r\n\r\n  if (mime && mime.charset) {\r\n    return mime.charset\r\n  }\r\n\r\n  // default text/* to utf-8\r\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\r\n    return 'UTF-8'\r\n  }\r\n\r\n  return false\r\n}\r\n\r\n/**\r\n * Create a full Content-Type header given a MIME type or extension.\r\n *\r\n * @param {string} str\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction contentType (str) {\r\n  // TODO: should this even be in this module?\r\n  if (!str || typeof str !== 'string') {\r\n    return false\r\n  }\r\n\r\n  var mime = str.indexOf('/') === -1\r\n    ? exports.lookup(str)\r\n    : str\r\n\r\n  if (!mime) {\r\n    return false\r\n  }\r\n\r\n  // TODO: use content-type or other module\r\n  if (mime.indexOf('charset') === -1) {\r\n    var charset = exports.charset(mime)\r\n    if (charset) mime += '; charset=' + charset.toLowerCase()\r\n  }\r\n\r\n  return mime\r\n}\r\n\r\n/**\r\n * Get the default extension for a MIME type.\r\n *\r\n * @param {string} type\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction extension (type) {\r\n  if (!type || typeof type !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // TODO: use media-typer\r\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\r\n\r\n  // get extensions\r\n  var exts = match && exports.extensions[match[1].toLowerCase()]\r\n\r\n  if (!exts || !exts.length) {\r\n    return false\r\n  }\r\n\r\n  return exts[0]\r\n}\r\n\r\n/**\r\n * Lookup the MIME type for a file path/extension.\r\n *\r\n * @param {string} path\r\n * @return {boolean|string}\r\n */\r\n\r\nfunction lookup (path) {\r\n  if (!path || typeof path !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // get the extension (\"ext\" or \".ext\" or full path)\r\n  var extension = extname('x.' + path)\r\n    .toLowerCase()\r\n    .substr(1)\r\n\r\n  if (!extension) {\r\n    return false\r\n  }\r\n\r\n  return exports.types[extension] || false\r\n}\r\n\r\n/**\r\n * Populate the extensions and types maps.\r\n * @private\r\n */\r\n\r\nfunction populateMaps (extensions, types) {\r\n  // source preference (least -> most)\r\n  var preference = ['nginx', 'apache', undefined, 'iana']\r\n\r\n  Object.keys(db).forEach(function forEachMimeType (type) {\r\n    var mime = db[type]\r\n    var exts = mime.extensions\r\n\r\n    if (!exts || !exts.length) {\r\n      return\r\n    }\r\n\r\n    // mime -> extensions\r\n    extensions[type] = exts\r\n\r\n    // extension -> mime\r\n    for (var i = 0; i < exts.length; i++) {\r\n      var extension = exts[i]\r\n\r\n      if (types[extension]) {\r\n        var from = preference.indexOf(db[types[extension]].source)\r\n        var to = preference.indexOf(mime.source)\r\n\r\n        if (types[extension] !== 'application/octet-stream' &&\r\n          (from > to || (from === to && types[extension].substr(0, 12) === 'application/'))) {\r\n          // skip the remapping\r\n          continue\r\n        }\r\n      }\r\n\r\n      // set the extension -> mime\r\n      types[extension] = type\r\n    }\r\n  })\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mime-types/index.js\n");

/***/ })

};
;