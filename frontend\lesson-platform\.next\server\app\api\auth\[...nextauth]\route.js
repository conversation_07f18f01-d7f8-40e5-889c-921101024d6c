/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n// src/app/api/auth/[...nextauth]/route.ts\n\n\n// Changed import: 'auth' is exported from firebase-admin, aliasing to firebaseAdminAuthService for clarity\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                firebaseToken: {\n                    label: \"Firebase ID Token\",\n                    type: \"text\"\n                },\n                studentId: {\n                    label: \"Student ID\",\n                    type: \"text\"\n                },\n                studentPassword: {\n                    label: \"Student Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                if (credentials?.firebaseToken) {\n                    try {\n                        // Use the imported firebaseAdminAuthService instance directly\n                        // No longer calling adminAuth() as it's not a function\n                        if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_2__.auth) {\n                            console.error(\"[NextAuth Authorize] Firebase Admin Auth service is not initialized.\");\n                            return null;\n                        }\n                        console.log(\"[NextAuth] Verifying Firebase ID token...\");\n                        // Use the instance directly\n                        const decodedToken = await _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_2__.auth.verifyIdToken(credentials.firebaseToken);\n                        console.log(\"[NextAuth] Firebase token verified successfully for UID:\", decodedToken.uid);\n                        console.log(\"[NextAuth Authorize] Firebase token verified. Returning user object.\");\n                        return {\n                            id: decodedToken.uid,\n                            email: decodedToken.email || null,\n                            role: 'parent',\n                            name: decodedToken.name || null\n                        };\n                    } catch (error) {\n                        console.error(\"[NextAuth] Firebase token verification failed:\", error);\n                        return null;\n                    }\n                } else if (credentials?.studentId && credentials?.studentPassword) {\n                    try {\n                        const baseUrl = process.env.NEXTAUTH_URL || (req?.headers?.host ? `${req.headers['x-forwarded-proto'] || 'http'}://${req.headers.host}` : 'http://localhost:3000');\n                        const studentLoginResponse = await fetch(`${baseUrl}/api/auth/student-login`, {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                studentId: credentials.studentId,\n                                password: credentials.studentPassword\n                            })\n                        });\n                        if (!studentLoginResponse.ok) {\n                            console.error(\"[NextAuth] Student login failed.\");\n                            return null;\n                        }\n                        const studentData = await studentLoginResponse.json(); // Assume this now includes firebaseCustomToken\n                        console.log(\"[NextAuth Authorize] Student login successful. Returning user object:\", studentData);\n                        return {\n                            id: studentData.id || studentData.studentId,\n                            name: studentData.name || null,\n                            role: 'student',\n                            email: null,\n                            firebaseCustomToken: studentData.firebaseCustomToken // Add this\n                        };\n                    } catch (error) {\n                        console.error(\"[NextAuth] Student login error:\", error);\n                        return null;\n                    }\n                }\n                console.error(\"[NextAuth] No valid credentials provided.\");\n                return null;\n            }\n        })\n    ],\n    // ... (session, callbacks, secret, debug, pages remain the same) ...\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            const typedUser = user;\n            console.log(\"[NextAuth JWT Callback] START - Received token:\", JSON.stringify(token));\n            if (typedUser) {\n                console.log(\"[NextAuth JWT Callback] User object present (initial sign-in):\", JSON.stringify(typedUser));\n                token.uid = typedUser.id;\n                token.role = typedUser.role;\n                console.log(`[NextAuth JWT Callback] Added uid ('${token.uid}') and role ('${token.role}') to token.`);\n            } else {\n                console.log(\"[NextAuth JWT Callback] User object NOT present (subsequent calls).\");\n            }\n            console.log(\"[NextAuth JWT Callback] END - Returning token:\", JSON.stringify(token));\n            return token;\n        },\n        async session ({ session, token }) {\n            const typedToken = token;\n            console.log(\"[NextAuth Session Callback] START - Received token:\", JSON.stringify(typedToken, null, 2));\n            console.log(\"[NextAuth Session Callback] START - Received session:\", JSON.stringify(session, null, 2));\n            if (!session.user) {\n                console.warn(\"[NextAuth Session Callback] session.user is initially undefined/null. This might indicate a type issue in next-auth.d.ts or session strategy problem.\");\n                session.user = {};\n            }\n            const sessionUser = session.user;\n            if (typedToken.uid) {\n                console.log(`[NextAuth Session Callback] Transferring uid ('${typedToken.uid}') from token to session.user.id`);\n                sessionUser.id = typedToken.uid;\n            } else {\n                console.log(\"[NextAuth Session Callback] Token did not contain 'uid'.\");\n            }\n            if (typedToken.role) {\n                console.log(`[NextAuth Session Callback] Transferring role ('${typedToken.role}') from token to session.user.role`);\n                sessionUser.role = typedToken.role;\n            } else {\n                console.log(\"[NextAuth Session Callback] Token did not contain 'role'.\");\n            }\n            console.log(\"[NextAuth Session Callback] END - Returning session:\", JSON.stringify(session, null, 2));\n            return session;\n        }\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === 'development'\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();