#!/usr/bin/env python3
"""
Test script to verify the enhanced data persistence functionality in the lesson manager.

This script tests:
1. Complete lesson summary data persistence to lesson_sessions collection
2. Teaching level data capture and persistence
3. Verification that all student-facing summary information is saved

Usage:
    python test_data_persistence_enhancements.py
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timezone
from typing import Dict, List, Any

# Add the lesson manager directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import required modules
try:
    from main import (
        save_complete_lesson_summary_to_firestore,
        generate_homework_assignments,
        db,
        logger
    )
    from firebase_admin import firestore
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the lesson_manager directory")
    sys.exit(1)

class DataPersistenceTest:
    """Test class for data persistence enhancements"""
    
    def __init__(self):
        self.test_session_id = f"test_session_{int(datetime.now().timestamp())}"
        self.test_student_id = "test_student_data_persistence"
        self.test_results = {
            'lesson_summary_saved': False,
            'teaching_level_captured': False,
            'homework_assignments_saved': False,
            'learning_objectives_saved': False,
            'performance_metrics_saved': False,
            'firestore_verification': False
        }
    
    def create_mock_lesson_context(self) -> Dict[str, Any]:
        """Create mock lesson context for testing"""
        return {
            'session_id': self.test_session_id,
            'student_id': self.test_student_id,
            'student_name': 'Test Student',
            'topic': 'Entrepreneurship Basics',
            'subject': 'Entrepreneurship',
            'grade': 'Primary 5',
            'key_concepts': [
                'Business planning fundamentals',
                'Market research basics',
                'Customer identification',
                'Product development concepts',
                'Financial planning basics'
            ],
            'learning_objectives': [
                'Understand the basic principles of entrepreneurship',
                'Identify potential business opportunities',
                'Develop a simple business plan',
                'Recognize the importance of market research'
            ],
            'quiz_answers': [
                {'question': 'What is entrepreneurship?', 'answer': 'Starting and running a business', 'is_correct': True},
                {'question': 'Why is market research important?', 'answer': 'To understand customers', 'is_correct': True},
                {'question': 'What is a business plan?', 'answer': 'A document outlining business goals', 'is_correct': True},
                {'question': 'Who are customers?', 'answer': 'People who buy products', 'is_correct': True},
                {'question': 'What is profit?', 'answer': 'Money earned after expenses', 'is_correct': False}
            ],
            'quiz_questions_generated': [
                'What is entrepreneurship?',
                'Why is market research important?',
                'What is a business plan?',
                'Who are customers?',
                'What is profit?'
            ],
            'estimated_duration': 30
        }
    
    async def test_homework_generation(self, lesson_context: Dict[str, Any], score_percentage: int) -> List[str]:
        """Test homework assignment generation"""
        print(f"\n📝 Testing homework generation for {score_percentage}% performance...")
        
        try:
            homework_assignments = await generate_homework_assignments(
                lesson_context, score_percentage, "test_request_001"
            )
            
            if homework_assignments and len(homework_assignments) > 0:
                print(f"✅ Generated {len(homework_assignments)} homework assignments:")
                for i, assignment in enumerate(homework_assignments, 1):
                    print(f"   {i}. {assignment}")
                self.test_results['homework_assignments_saved'] = True
                return homework_assignments
            else:
                print("❌ No homework assignments generated")
                return []
                
        except Exception as e:
            print(f"❌ Error generating homework: {e}")
            return []
    
    async def test_lesson_summary_persistence(self, lesson_context: Dict[str, Any], 
                                            homework_assignments: List[str], 
                                            score_percentage: int, teaching_level: int):
        """Test complete lesson summary persistence"""
        print(f"\n💾 Testing lesson summary persistence...")
        
        try:
            # Create mock data for summary
            concepts_covered = lesson_context['key_concepts'][:5]
            objectives_achieved = lesson_context['learning_objectives'][:3]
            
            # Generate next steps based on performance
            if score_percentage >= 80:
                next_steps = [
                    f"Practice more {lesson_context['topic']} problems",
                    f"Explore related {lesson_context['subject']} concepts",
                    f"Connect {lesson_context['topic']} to other subjects"
                ]
            else:
                next_steps = [
                    f"Review key {lesson_context['topic']} concepts",
                    f"Practice basic {lesson_context['topic']} applications",
                    f"Ask questions about unclear areas"
                ]
            
            # Test the save function
            await save_complete_lesson_summary_to_firestore(
                self.test_session_id, self.test_student_id, lesson_context,
                homework_assignments, score_percentage, concepts_covered,
                objectives_achieved, next_steps, teaching_level, "test_request_001"
            )
            
            print("✅ Lesson summary persistence function executed successfully")
            self.test_results['lesson_summary_saved'] = True
            
        except Exception as e:
            print(f"❌ Error in lesson summary persistence: {e}")
    
    async def verify_firestore_data(self):
        """Verify that data was actually saved to Firestore"""
        print(f"\n🔍 Verifying Firestore data persistence...")
        
        try:
            if not db:
                print("❌ Firestore database not available")
                return
            
            # Get the session document
            session_ref = db.collection('lesson_sessions').document(self.test_session_id)
            session_doc = session_ref.get()
            
            if not session_doc.exists:
                print(f"❌ Session document not found: {self.test_session_id}")
                return
            
            session_data = session_doc.to_dict()
            
            # Check for lesson completion data
            if session_data.get('lesson_completed'):
                print("✅ Lesson completion status saved")
                
            # Check for student summary data
            student_summary = session_data.get('student_summary')
            if student_summary:
                print("✅ Student summary data saved")
                print(f"   - Topic: {student_summary.get('topic')}")
                print(f"   - Teaching level: {student_summary.get('teaching_level')}")
                print(f"   - Concepts covered: {len(student_summary.get('concepts_covered', []))}")
                print(f"   - Objectives achieved: {len(student_summary.get('objectives_achieved', []))}")
                print(f"   - Homework assignments: {len(student_summary.get('homework_assignments', []))}")
                print(f"   - Next steps: {len(student_summary.get('next_steps', []))}")
                
                self.test_results['learning_objectives_saved'] = True
                self.test_results['performance_metrics_saved'] = True
            
            # Check for teaching level data
            teaching_level = session_data.get('teaching_level')
            teaching_level_metadata = session_data.get('teaching_level_metadata')
            if teaching_level is not None:
                print(f"✅ Teaching level captured: {teaching_level}")
                if teaching_level_metadata:
                    print(f"   - Assignment source: {teaching_level_metadata.get('assignment_source')}")
                    print(f"   - Assigned timestamp: {teaching_level_metadata.get('assigned_timestamp')}")
                self.test_results['teaching_level_captured'] = True
            
            # Check for lesson analytics
            lesson_analytics = session_data.get('lesson_analytics')
            if lesson_analytics:
                print("✅ Lesson analytics saved")
                print(f"   - Diagnostic level assigned: {lesson_analytics.get('diagnostic_level_assigned')}")
                print(f"   - Final assessment level: {lesson_analytics.get('final_assessment_level')}")
            
            self.test_results['firestore_verification'] = True
            print("✅ Firestore data verification completed successfully")
            
        except Exception as e:
            print(f"❌ Error verifying Firestore data: {e}")
    
    async def cleanup_test_data(self):
        """Clean up test data from Firestore"""
        print(f"\n🧹 Cleaning up test data...")
        
        try:
            if db:
                # Delete test session document
                session_ref = db.collection('lesson_sessions').document(self.test_session_id)
                session_ref.delete()
                print(f"✅ Deleted test session: {self.test_session_id}")
        except Exception as e:
            print(f"⚠️ Error cleaning up test data: {e}")
    
    def print_test_results(self):
        """Print comprehensive test results"""
        print(f"\n" + "="*60)
        print("📊 DATA PERSISTENCE ENHANCEMENT TEST RESULTS")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"Overall: {passed_tests}/{total_tests} tests passed")
        print()
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name.replace('_', ' ').title()}")
        
        print()
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Data persistence enhancements are working correctly.")
        else:
            print(f"⚠️ {total_tests - passed_tests} tests failed. Please review the implementation.")
        
        print("="*60)

async def main():
    """Main test function"""
    print("🚀 Starting Data Persistence Enhancement Tests...")
    print("="*60)
    
    test = DataPersistenceTest()
    
    # Create mock lesson context
    lesson_context = test.create_mock_lesson_context()
    score_percentage = 80  # Good performance
    teaching_level = 6     # Proficient level
    
    print(f"📋 Test Configuration:")
    print(f"   Session ID: {test.test_session_id}")
    print(f"   Student ID: {test.test_student_id}")
    print(f"   Topic: {lesson_context['topic']}")
    print(f"   Score: {score_percentage}%")
    print(f"   Teaching Level: {teaching_level}")
    
    try:
        # Test 1: Homework generation
        homework_assignments = await test.test_homework_generation(lesson_context, score_percentage)
        
        # Test 2: Lesson summary persistence
        await test.test_lesson_summary_persistence(
            lesson_context, homework_assignments, score_percentage, teaching_level
        )
        
        # Test 3: Firestore verification
        await test.verify_firestore_data()
        
        # Print results
        test.print_test_results()
        
        # Cleanup
        await test.cleanup_test_data()
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
