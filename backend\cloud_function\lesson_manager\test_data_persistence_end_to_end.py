#!/usr/bin/env python3
"""
End-to-end test for data persistence in lesson completion flow.

This script tests the complete lesson flow from conclusion_summary phase
to verify that all student-facing data is properly saved to Firestore.

Usage:
    python test_data_persistence_end_to_end.py
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timezone
from typing import Dict, List, Any

# Add the lesson manager directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import required modules
try:
    from main import (
        handle_conclusion_summary_phase,
        save_complete_lesson_summary_to_firestore,
        get_gemini_model,
        db,
        logger
    )
    from firebase_admin import firestore
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the lesson_manager directory")
    sys.exit(1)

class DataPersistenceEndToEndTest:
    """End-to-end test for data persistence during lesson completion"""
    
    def __init__(self):
        self.test_session_id = f"test_e2e_{int(datetime.now().timestamp())}"
        self.test_student_id = "test_student_e2e_persistence"
        self.test_results = {
            'conclusion_phase_executed': False,
            'save_function_called': False,
            'teaching_level_captured': False,
            'student_summary_saved': False,
            'homework_assignments_saved': False,
            'performance_metrics_saved': False,
            'firestore_data_verified': False,
            'data_matches_summary': False
        }
    
    def create_comprehensive_lesson_context(self) -> Dict[str, Any]:
        """Create a comprehensive lesson context that simulates a completed lesson"""
        return {
            'session_id': self.test_session_id,
            'student_id': self.test_student_id,
            'student_name': 'Andrea Test Student',
            'topic': 'Advanced Coding',
            'subject': 'Computing',
            'grade': 'Primary 5',
            'key_concepts': [
                'Design', 'write', 'debug', 'programs', 'solve',
                'Variables and functions', 'Conditional statements',
                'Loops and iteration', 'Problem decomposition'
            ],
            'learning_objectives': [
                'Design, write, and debug programs to solve specific problems',
                'Incorporate variables and functions in coding projects',
                'Use conditional statements to control program flow',
                'Apply loops for repetitive tasks'
            ],
            'quiz_answers': [
                {'question': 'What is a variable?', 'answer': 'A container for storing data', 'is_correct': True},
                {'question': 'What is a function?', 'answer': 'A reusable block of code', 'is_correct': True},
                {'question': 'What is debugging?', 'answer': 'Finding and fixing errors', 'is_correct': True},
                {'question': 'What is a loop?', 'answer': 'Code that repeats', 'is_correct': True},
                {'question': 'What is an algorithm?', 'answer': 'Step by step instructions', 'is_correct': True},
                {'question': 'What is pseudocode?', 'answer': 'Planning code in plain language', 'is_correct': True},
                {'question': 'What is syntax?', 'answer': 'Rules for writing code', 'is_correct': True},
                {'question': 'What is a conditional?', 'answer': 'If-then statements', 'is_correct': True},
                {'question': 'What is iteration?', 'answer': 'Repeating processes', 'is_correct': True},
                {'question': 'What is decomposition?', 'answer': 'Breaking problems into parts', 'is_correct': False}
            ],
            'quiz_questions_generated': [
                'What is a variable?', 'What is a function?', 'What is debugging?',
                'What is a loop?', 'What is an algorithm?', 'What is pseudocode?',
                'What is syntax?', 'What is a conditional?', 'What is iteration?',
                'What is decomposition?'
            ],
            'assigned_level_for_teaching': 6,  # This should be captured
            'teaching_level_metadata': {
                'level': 6,
                'assigned_timestamp': datetime.now(timezone.utc).isoformat(),
                'assignment_source': 'diagnostic_completion',
                'diagnostic_completed': True
            }
        }
    
    async def create_test_session_in_firestore(self):
        """Create a test session in Firestore with teaching level data"""
        print(f"\n📝 Creating test session in Firestore...")
        
        try:
            if not db:
                print("❌ Firestore database not available")
                return False
            
            # Create session document with teaching level
            session_data = {
                'session_id': self.test_session_id,
                'student_id': self.test_student_id,
                'status': 'active',
                'created_at': firestore.SERVER_TIMESTAMP,
                'current_phase': 'conclusion_summary',
                'teaching_level': 6,  # This should be retrieved
                'teaching_level_metadata': {
                    'level': 6,
                    'assigned_timestamp': datetime.now(timezone.utc).isoformat(),
                    'assignment_source': 'diagnostic_completion',
                    'diagnostic_completed': True
                },
                'lesson_completed': False,
                'student_summary': None,
                'lesson_analytics': None
            }
            
            session_ref = db.collection('lesson_sessions').document(self.test_session_id)
            session_ref.set(session_data)
            
            print(f"✅ Test session created: {self.test_session_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating test session: {e}")
            return False
    
    async def test_conclusion_summary_phase(self):
        """Test the conclusion summary phase execution"""
        print(f"\n🎓 Testing conclusion summary phase execution...")
        
        try:
            # Create lesson context
            lesson_context = self.create_comprehensive_lesson_context()
            
            # Get Gemini model
            model = get_gemini_model()
            if not model:
                print("❌ Gemini model not available")
                return False
            
            # Execute conclusion summary phase
            print(f"   Calling handle_conclusion_summary_phase...")
            response, next_phase, state_updates = await handle_conclusion_summary_phase(
                "test_request_e2e", 
                "Thank you for the great lesson! Can you summarize what I learned?",
                lesson_context,
                model
            )
            
            print(f"✅ Conclusion summary phase executed successfully")
            print(f"   Response length: {len(response)} characters")
            print(f"   Next phase: {next_phase}")
            print(f"   State updates: {list(state_updates.keys()) if state_updates else 'None'}")
            
            # Check if response contains expected summary elements
            expected_elements = [
                'Key Concepts', 'Learning Objectives', 'Performance', 
                'Homework', 'Congratulations', 'Advanced Coding'
            ]
            
            elements_found = sum(1 for element in expected_elements if element in response)
            print(f"   Summary elements found: {elements_found}/{len(expected_elements)}")
            
            self.test_results['conclusion_phase_executed'] = True
            return True
            
        except Exception as e:
            print(f"❌ Error in conclusion summary phase: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_firestore_data_persistence(self):
        """Verify that data was properly saved to Firestore"""
        print(f"\n🔍 Verifying Firestore data persistence...")
        
        try:
            if not db:
                print("❌ Firestore database not available")
                return False
            
            # Get the session document
            session_ref = db.collection('lesson_sessions').document(self.test_session_id)
            session_doc = session_ref.get()
            
            if not session_doc.exists:
                print(f"❌ Session document not found: {self.test_session_id}")
                return False
            
            session_data = session_doc.to_dict()
            print(f"✅ Session document retrieved")
            
            # Check lesson completion status
            lesson_completed = session_data.get('lesson_completed', False)
            print(f"   Lesson completed: {lesson_completed}")
            
            # Check teaching level data
            teaching_level = session_data.get('teaching_level')
            teaching_level_metadata = session_data.get('teaching_level_metadata')
            print(f"   Teaching level: {teaching_level}")
            print(f"   Teaching level metadata: {bool(teaching_level_metadata)}")
            
            if teaching_level is not None:
                self.test_results['teaching_level_captured'] = True
            
            # Check student summary data
            student_summary = session_data.get('student_summary')
            if student_summary:
                print(f"✅ Student summary data found")
                print(f"   Topic: {student_summary.get('topic')}")
                print(f"   Teaching level: {student_summary.get('teaching_level')}")
                print(f"   Concepts covered: {len(student_summary.get('concepts_covered', []))}")
                print(f"   Objectives achieved: {len(student_summary.get('objectives_achieved', []))}")
                print(f"   Homework assignments: {len(student_summary.get('homework_assignments', []))}")
                print(f"   Next steps: {len(student_summary.get('next_steps', []))}")
                
                # Check quiz performance
                quiz_performance = student_summary.get('quiz_performance', {})
                if quiz_performance:
                    print(f"   Quiz score: {quiz_performance.get('score_percentage')}%")
                    print(f"   Quiz display: {quiz_performance.get('quiz_score_display')}")
                    self.test_results['performance_metrics_saved'] = True
                
                # Check homework assignments
                homework_assignments = student_summary.get('homework_assignments', [])
                if homework_assignments and len(homework_assignments) > 0:
                    print(f"   Homework assignments: {len(homework_assignments)} found")
                    for i, hw in enumerate(homework_assignments[:3], 1):
                        print(f"     {i}. {hw[:50]}...")
                    self.test_results['homework_assignments_saved'] = True
                
                self.test_results['student_summary_saved'] = True
            else:
                print(f"❌ No student summary data found")
            
            # Check lesson analytics
            lesson_analytics = session_data.get('lesson_analytics')
            if lesson_analytics:
                print(f"✅ Lesson analytics found")
                print(f"   Diagnostic level assigned: {lesson_analytics.get('diagnostic_level_assigned')}")
                print(f"   Final assessment level: {lesson_analytics.get('final_assessment_level')}")
                print(f"   Completion date: {lesson_analytics.get('completion_date')}")
            
            self.test_results['firestore_data_verified'] = True
            return True
            
        except Exception as e:
            print(f"❌ Error verifying Firestore data: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Clean up test data from Firestore"""
        print(f"\n🧹 Cleaning up test data...")
        
        try:
            if db:
                session_ref = db.collection('lesson_sessions').document(self.test_session_id)
                session_ref.delete()
                print(f"✅ Deleted test session: {self.test_session_id}")
        except Exception as e:
            print(f"⚠️ Error cleaning up test data: {e}")
    
    def print_test_results(self):
        """Print comprehensive test results"""
        print(f"\n" + "="*70)
        print("📊 DATA PERSISTENCE END-TO-END TEST RESULTS")
        print("="*70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"Overall: {passed_tests}/{total_tests} tests passed")
        print()
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name.replace('_', ' ').title()}")
        
        print()
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Data persistence is working correctly.")
            print("   ✅ Lesson completion data is being saved to Firestore")
            print("   ✅ Teaching level is being captured and persisted")
            print("   ✅ Student summary information is being saved")
            print("   ✅ Performance metrics and homework are being stored")
        else:
            print(f"⚠️ {total_tests - passed_tests} tests failed. Issues found:")
            for test_name, result in self.test_results.items():
                if not result:
                    print(f"   ❌ {test_name.replace('_', ' ').title()}")
        
        print("="*70)

async def main():
    """Main test function"""
    print("🚀 Starting Data Persistence End-to-End Test...")
    print("="*70)
    
    test = DataPersistenceEndToEndTest()
    
    print(f"📋 Test Configuration:")
    print(f"   Session ID: {test.test_session_id}")
    print(f"   Student ID: {test.test_student_id}")
    print(f"   Topic: Advanced Coding")
    print(f"   Expected Score: 90% (9/10 correct)")
    print(f"   Expected Teaching Level: 6")
    
    try:
        # Step 1: Create test session in Firestore
        session_created = await test.create_test_session_in_firestore()
        if not session_created:
            print("❌ Failed to create test session, aborting test")
            return
        
        # Step 2: Test conclusion summary phase
        conclusion_success = await test.test_conclusion_summary_phase()
        
        # Step 3: Verify Firestore data persistence
        if conclusion_success:
            await test.verify_firestore_data_persistence()
        
        # Step 4: Print results
        test.print_test_results()
        
        # Step 5: Cleanup
        await test.cleanup_test_data()
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
