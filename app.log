[2025-07-01 13:08:54,348] INFO - main - main.py:695 - ================================================================================
[2025-07-01 13:08:54,349] INFO - main - main.py:696 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 13:08:54,350] INFO - main - main.py:697 - ================================================================================
[2025-07-01 13:08:54,350] INFO - main - main.py:698 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 13:08:54,351] INFO - main - main.py:699 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
[2025-07-01 13:08:54,353] INFO - main - main.py:700 - Log level: DEBUG
[2025-07-01 13:08:54,354] INFO - main - main.py:701 - ================================================================================
[2025-07-01 13:08:54,355] INFO - main - main.py:703 - Logging configuration complete with immediate console output
[2025-07-01 13:08:54,356] INFO - main - main.py:704 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 13:08:54,361] INFO - main - main.py:779 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 13:08:54,364] INFO - main - main.py:958 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 13:08:54,389] INFO - main - main.py:987 - Phase transition fixes imported successfully
[2025-07-01 13:08:54,395] INFO - main - main.py:3367 - Successfully imported utils functions
[2025-07-01 13:08:54,398] INFO - main - main.py:3375 - Successfully imported extract_ai_state functions
[2025-07-01 13:08:54,410] INFO - main - main.py:3825 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 13:08:54,413] INFO - unified_firebase_init - unified_firebase_init.py:79 - DEBUG: Environment variable GOOGLE_APPLICATION_CREDENTIALS = C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:08:54,414] INFO - unified_firebase_init - unified_firebase_init.py:80 - DEBUG: Forcing use of correct service account: solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json
[2025-07-01 13:08:54,418] INFO - unified_firebase_init - unified_firebase_init.py:97 - Fallback: Attempting Firebase initialization with environment credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:08:54,515] INFO - unified_firebase_init - unified_firebase_init.py:102 - ✅ Firebase initialized successfully with environment credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:08:54,516] INFO - unified_firebase_init - unified_firebase_init.py:153 - Testing Firestore connectivity with lightweight operation...
[2025-07-01 13:08:55,151] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:55,158] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-07-01 13:08:55,784] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:55,790] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:55,885] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.1s ...
[2025-07-01 13:08:55,955] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:56,284] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:56,287] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:56,347] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.0s ...
[2025-07-01 13:08:56,395] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:56,549] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:56,552] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:56,601] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.1s ...
[2025-07-01 13:08:56,682] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:56,830] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:56,832] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:56,868] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.2s ...
[2025-07-01 13:08:57,033] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:57,185] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:57,187] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:57,214] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.3s ...
[2025-07-01 13:08:57,491] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:57,646] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:57,647] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:57,669] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.2s ...
[2025-07-01 13:08:57,876] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:58,064] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:58,067] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:58,112] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.0s ...
[2025-07-01 13:08:58,155] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:58,320] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:58,322] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:58,359] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.2s ...
[2025-07-01 13:08:58,522] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:58,688] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:58,689] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:58,710] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.7s ...
[2025-07-01 13:08:59,461] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:08:59,618] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:08:59,619] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:08:59,658] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.7s ...
[2025-07-01 13:09:00,366] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:09:00,589] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:09:00,591] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:09:00,623] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.2s ...
[2025-07-01 13:09:00,876] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:09:01,087] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:09:01,089] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:09:01,128] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 1.5s ...
[2025-07-01 13:09:02,620] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:09:02,806] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:09:02,807] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:09:02,826] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 2.0s ...
[2025-07-01 13:09:04,839] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:09:04,982] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:09:04,983] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:09:05,002] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 1.2s ...
[2025-07-01 13:09:06,234] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:09:06,596] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:09:06,606] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x00000240760F1160>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:09:06,644] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 3.2s ...
[2025-07-01 13:09:09,521] WARNING - unified_firebase_init - unified_firebase_init.py:185 - Firestore connectivity test timed out after 15 seconds - continuing with degraded mode
[2025-07-01 13:09:09,521] INFO - main - main.py:3833 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 13:09:09,522] INFO - main - main.py:3923 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 13:09:09,549] INFO - main - main.py:1201 - Successfully imported timetable_generator functions
