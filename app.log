[2025-07-01 13:54:49,672] INFO - main - main.py:695 - ================================================================================
[2025-07-01 13:54:49,673] INFO - main - main.py:696 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 13:54:49,674] INFO - main - main.py:697 - ================================================================================
[2025-07-01 13:54:49,674] INFO - main - main.py:698 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 13:54:49,677] INFO - main - main.py:699 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
[2025-07-01 13:54:49,679] INFO - main - main.py:700 - Log level: DEBUG
[2025-07-01 13:54:49,680] INFO - main - main.py:701 - ================================================================================
[2025-07-01 13:54:49,682] INFO - main - main.py:703 - Logging configuration complete with immediate console output
[2025-07-01 13:54:49,683] INFO - main - main.py:704 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 13:54:49,687] INFO - main - main.py:779 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 13:54:49,690] INFO - main - main.py:958 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 13:54:49,707] INFO - main - main.py:987 - Phase transition fixes imported successfully
[2025-07-01 13:54:49,715] INFO - main - main.py:3367 - Successfully imported utils functions
[2025-07-01 13:54:49,718] INFO - main - main.py:3375 - Successfully imported extract_ai_state functions
[2025-07-01 13:54:49,729] INFO - main - main.py:3825 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 13:54:49,734] INFO - unified_firebase_init - unified_firebase_init.py:79 - DEBUG: Environment variable GOOGLE_APPLICATION_CREDENTIALS = C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:54:49,735] INFO - unified_firebase_init - unified_firebase_init.py:80 - DEBUG: Forcing use of correct service account: solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json
[2025-07-01 13:54:49,738] INFO - unified_firebase_init - unified_firebase_init.py:97 - Fallback: Attempting Firebase initialization with environment credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:54:49,809] INFO - unified_firebase_init - unified_firebase_init.py:102 - ✅ Firebase initialized successfully with environment credentials: C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json
[2025-07-01 13:54:49,810] INFO - unified_firebase_init - unified_firebase_init.py:153 - Testing Firestore connectivity with lightweight operation...
[2025-07-01 13:54:50,510] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:50,515] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-07-01 13:54:50,987] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:50,992] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:51,089] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.1s ...
[2025-07-01 13:54:51,175] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:51,423] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:51,426] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:51,464] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.1s ...
[2025-07-01 13:54:51,522] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:51,699] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:51,700] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:51,723] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.1s ...
[2025-07-01 13:54:51,811] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:51,957] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:51,962] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:51,981] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.0s ...
[2025-07-01 13:54:52,012] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:52,187] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:52,191] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:52,233] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.0s ...
[2025-07-01 13:54:52,282] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:52,440] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:52,441] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:52,463] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.3s ...
[2025-07-01 13:54:52,775] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:53,000] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:53,003] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:53,043] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.2s ...
[2025-07-01 13:54:53,267] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:53,437] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:53,439] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:53,471] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.3s ...
[2025-07-01 13:54:53,810] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:54,056] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:54,058] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:54,102] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.5s ...
[2025-07-01 13:54:54,560] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:54,710] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:54,711] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:54,738] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.7s ...
[2025-07-01 13:54:55,433] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:55,583] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:55,585] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:55,627] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 1.2s ...
[2025-07-01 13:54:56,830] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:57,063] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:57,067] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:57,107] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.5s ...
[2025-07-01 13:54:57,621] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:57,763] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:57,767] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:57,800] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 0.5s ...
[2025-07-01 13:54:58,347] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:54:58,643] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:54:58,645] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:54:58,690] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 1.7s ...
[2025-07-01 13:55:00,369] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:55:00,519] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:55:00,523] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:55:00,573] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 3.4s ...
[2025-07-01 13:55:04,001] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-07-01 13:55:04,225] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
[2025-07-01 13:55:04,227] ERROR - grpc._plugin_wrapping - _plugin_wrapping.py:109 - AuthMetadataPluginCallback "<google.auth.transport.grpc.AuthMetadataPlugin object at 0x000002727E0B82F0>" raised exception!
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\grpc\_plugin_wrapping.py", line 105, in __call__
    self._metadata_plugin(
    ~~~~~~~~~~~~~~~~~~~~~^
        context, _AuthMetadataPluginCallback(callback_state, callback)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 95, in __call__
    callback(self._get_authorization_headers(context), None)
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\transport\grpc.py", line 81, in _get_authorization_headers
    self._credentials.before_request(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._request, context.method_name, context.service_url, headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
    ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ~~~~~~~~~~~~~~~~~^
        request, self._token_uri, assertion
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
        request,
    ...<5 lines>...
        },
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\oauth2\_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
        error_details, response_data, retryable=retryable_error
    )
google.auth.exceptions.RefreshError: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'})
[2025-07-01 13:55:04,257] DEBUG - google.api_core.retry - retry_base.py:222 - Retrying due to 503 Getting metadata from plugin failed with error: ('invalid_grant: Invalid grant: account not found', {'error': 'invalid_grant', 'error_description': 'Invalid grant: account not found'}), sleeping 2.7s ...
[2025-07-01 13:55:04,815] WARNING - unified_firebase_init - unified_firebase_init.py:185 - Firestore connectivity test timed out after 15 seconds - continuing with degraded mode
[2025-07-01 13:55:04,816] INFO - main - main.py:3833 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 13:55:04,817] INFO - main - main.py:3923 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 13:55:04,842] INFO - main - main.py:1201 - Successfully imported timetable_generator functions
