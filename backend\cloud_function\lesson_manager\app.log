[2025-07-01 11:50:45,907] INFO - main - main.py:138 - ================================================================================
[2025-07-01 11:50:45,908] INFO - main - main.py:139 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 11:50:45,908] INFO - main - main.py:140 - Log level: DEBUG
[2025-07-01 11:50:45,908] INFO - main - main.py:141 - LOG SETUP COMPLETE - Aggressive console output is active.
[2025-07-01 11:50:45,908] INFO - main - main.py:142 - ================================================================================
[2025-07-01 11:50:45,908] INFO - main - main.py:149 - 🔍 CONSOLE LOGGING ENABLED - DIAGNOSTIC DEBUGGING ACTIVE
[2025-07-01 11:50:45,920] INFO - main - main.py:207 - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
[2025-07-01 11:50:45,920] INFO - main - main.py:519 - INIT_INFO: Firebase initialization deferred until server startup or first use
[2025-07-01 11:50:45,920] INFO - main - main.py:619 - ================================================================================
[2025-07-01 11:50:45,921] INFO - main - main.py:620 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 11:50:45,921] INFO - main - main.py:621 - ================================================================================
[2025-07-01 11:50:45,921] INFO - main - main.py:622 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 11:50:45,921] INFO - main - main.py:623 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
[2025-07-01 11:50:45,921] INFO - main - main.py:624 - Log level: DEBUG
[2025-07-01 11:50:45,921] INFO - main - main.py:625 - ================================================================================
[2025-07-01 11:50:45,921] INFO - main - main.py:627 - Logging configuration complete with immediate console output
[2025-07-01 11:50:45,921] INFO - main - main.py:628 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 11:50:45,924] INFO - main - main.py:703 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 11:50:45,931] INFO - main - main.py:882 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 11:50:45,932] INFO - main - main.py:911 - Phase transition fixes imported successfully
[2025-07-01 11:50:45,937] INFO - main - main.py:3208 - Successfully imported utils functions
[2025-07-01 11:50:45,937] INFO - main - main.py:3216 - Successfully imported extract_ai_state functions
[2025-07-01 11:50:45,941] INFO - main - main.py:3666 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 11:50:45,941] INFO - unified_firebase_init - unified_firebase_init.py:65 - Firebase already initialized
[2025-07-01 11:50:45,941] INFO - main - main.py:3674 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 11:50:45,941] INFO - main - main.py:3764 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 11:50:45,966] INFO - main - main.py:1052 - Successfully imported timetable_generator functions
[2025-07-01 11:50:45,977] WARNING - auth_decorator - auth_decorator.py:56 - Could not fetch student name from Firestore: View function mapping is overwriting an existing endpoint function: static_files
[2025-07-01 11:50:45,979] INFO - auth_decorator - auth_decorator.py:160 - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Development Student
[2025-07-01 11:50:45,979] INFO - auth_decorator - auth_decorator.py:164 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1][require_auth] Development auth: uid=andrea_ugono_33305, name=Development Student
[2025-07-01 11:50:45,982] INFO - __main__ - main.py:5213 - 
================================================================================
[2025-07-01 11:50:45,982] WARNING - __main__ - main.py:5213 - 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
[2025-07-01 11:50:45,982] WARNING - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-07-01 11:50:45,982] INFO - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-001","content_to_enhance":"Yes","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","subject":"Artificial Intelligence","session_id":"session_35197f7f-9cfc-4675-8a84-badd21f85cb0","chat_history":[{"role":"user","content":"Smart thermostats that turn off when nobody's home.","timestamp":"2025-07-01T10:48:45.220Z"},{"role":"assistant","content":"**Question 9 of 10 (Short Answer)**\n\n*What does it mean to 'e...
[2025-07-01 11:50:45,983] INFO - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': 'Yes', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'session_id': 'session_35197f7f-9cfc-4675-8a84-badd21f85cb0', 'chat_history': [{'role': 'user', 'content': "Smart thermostats that turn off when nobody's home.", 'timestamp': '2025-07-01T10:48:45.220Z'}, {'role': 'assistant', 'content': "**Question 9 of 10 (Short Answer)**\n\n*What does it mean to 'explore' how AI helps the planet?*", 'timestamp': '2025-07-01T10:48:49.505Z'}, {'role': 'user', 'content': 'It means learning about all the different ways AI can help make the planet better.', 'timestamp': '2025-07-01T10:49:48.953Z'}, {'role': 'assistant', 'content': '**Question 10 of 10 (Short Answer)**\n\n*Explain one role AI can have in saving the planet.*', 'timestamp': '2025-07-01T10:49:52.931Z'}, {'role': 'user', 'content': 'AI can help protect wildlife by tracking endangered animals.', 'timestamp': '2025-07-01T10:50:29.530Z'}, {'role': 'assistant', 'content': "🎉 Excellent work, Andrea! You've completed the quiz. Let me compile your results.", 'timestamp': '2025-07-01T10:50:33.798Z'}, {'role': 'user', 'content': 'ok', 'timestamp': '2025-07-01T10:50:37.615Z'}, {'role': 'assistant', 'content': "🎯 **Quiz Results for How AI helps the planet**\n\nOutstanding! 🌟 Student! Here's your detailed performance analysis:\n- Score: 100% (10/10 correct)\n- You've successfully completed the quiz portion of the lesson!\n\nReady to wrap up with a summary of everything you've learned?", 'timestamp': '2025-07-01T10:50:41.614Z'}]}
[2025-07-01 11:50:45,983] INFO - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍  - Session ID from payload: session_35197f7f-9cfc-4675-8a84-badd21f85cb0
[2025-07-01 11:50:45,983] INFO - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍  - Student ID from payload: andrea_ugono_33305
[2025-07-01 11:50:45,983] INFO - __main__ - main.py:5213 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍  - Lesson Ref from payload: P5-AI-001
[2025-07-01 11:50:45,983] DEBUG - __main__ - main.py:5270 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='session_35197f7f-9cfc-4675-8a84-badd21f85cb0', lesson_ref='P5-AI-001'
[2025-07-01 11:50:45,984] INFO - __main__ - main.py:5271 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Parsed Params: student_id='andrea_ugono_33305', session_id='session_35197f7f-9cfc-4675-8a84-badd21f85cb0', lesson_ref='P5-AI-001'
[2025-07-01 11:50:46,288] INFO - __main__ - main.py:4561 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-07-01 11:50:46,288] DEBUG - __main__ - main.py:661 - Cache hit for fetch_lesson_data
[2025-07-01 11:50:46,288] INFO - __main__ - main.py:5321 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ All required fields present after lesson content parsing and mapping
[2025-07-01 11:50:46,288] INFO - __main__ - main.py:5360 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-07-01 11:50:46,288] INFO - __main__ - main.py:2342 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-07-01 11:50:46,780] INFO - __main__ - main.py:2401 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-07-01 11:50:46,781] INFO - __main__ - main.py:2401 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-07-01 11:50:46,781] INFO - __main__ - main.py:2401 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-07-01 11:50:46,781] INFO - __main__ - main.py:2401 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-07-01 11:50:46,781] INFO - __main__ - main.py:2401 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-07-01 11:50:46,781] INFO - __main__ - main.py:2470 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-07-01 11:50:46,781] DEBUG - __main__ - main.py:2484 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-07-01 11:50:46,781] DEBUG - __main__ - main.py:2487 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-07-01 11:50:46,781] DEBUG - __main__ - main.py:2488 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-07-01 11:50:46,782] DEBUG - __main__ - main.py:2489 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-07-01 11:50:46,782] INFO - __main__ - main.py:2493 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Calling Gemini API for module inference...
[2025-07-01 11:50:47,285] INFO - __main__ - main.py:2503 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Gemini API call completed in 0.50s. Raw response: 'ai_tools_and_applications'
[2025-07-01 11:50:47,286] DEBUG - __main__ - main.py:2525 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-07-01 11:50:47,286] INFO - __main__ - main.py:2530 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-07-01 11:50:47,286] INFO - __main__ - main.py:5394 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-07-01 11:50:47,286] INFO - __main__ - main.py:5431 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-07-01 11:50:47,580] INFO - __main__ - main.py:2060 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-07-01 11:50:48,107] DEBUG - __main__ - main.py:5447 - 🔍 SESSION STATE RETRIEVAL:
[2025-07-01 11:50:48,107] DEBUG - __main__ - main.py:5448 - 🔍   - Session ID: session_35197f7f-9cfc-4675-8a84-badd21f85cb0
[2025-07-01 11:50:48,108] DEBUG - __main__ - main.py:5449 - 🔍   - Document Exists: True
[2025-07-01 11:50:48,109] DEBUG - __main__ - main.py:5450 - 🔍   - Current Phase: conclusion_summary
[2025-07-01 11:50:48,109] DEBUG - __main__ - main.py:5451 - 🔍   - Probing Level: 5
[2025-07-01 11:50:48,109] DEBUG - __main__ - main.py:5452 - 🔍   - Question Index: 0
[2025-07-01 11:50:48,109] WARNING - __main__ - main.py:5458 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 SESSION STATE DEBUG:
[2025-07-01 11:50:48,110] WARNING - __main__ - main.py:5459 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   - Session exists: True
[2025-07-01 11:50:48,110] WARNING - __main__ - main.py:5460 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   - Current phase: conclusion_summary
[2025-07-01 11:50:48,110] WARNING - __main__ - main.py:5461 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'teaching_complete', 'assigned_level_for_teaching', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-07-01 11:50:48,110] DEBUG - __main__ - main.py:5480 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
[2025-07-01 11:50:48,111] DEBUG - __main__ - main.py:5481 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] � Retrieved Phase: 'conclusion_summary'
[2025-07-01 11:50:48,111] DEBUG - __main__ - main.py:5482 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] � Diagnostic Completed: False
[2025-07-01 11:50:48,111] DEBUG - __main__ - main.py:5483 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] � Assigned Level: None
[2025-07-01 11:50:48,111] WARNING - __main__ - main.py:5484 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔒 STATE PROTECTION: phase='conclusion_summary', diagnostic_done=False, level=None
[2025-07-01 11:50:48,111] INFO - __main__ - main.py:5528 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ State protection not triggered (diagnostic=False, level=None)
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5529 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] State protection not triggered
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5570 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] �🔍 FIRST ENCOUNTER LOGIC:
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5571 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   assigned_level_for_teaching (session): None
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5572 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   latest_assessed_level (profile): None
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5573 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   teaching_level_for_returning_student: None
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5574 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   has_completed_diagnostic_before: False
[2025-07-01 11:50:48,112] INFO - __main__ - main.py:5575 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   is_first_encounter_for_module: True
[2025-07-01 11:50:48,113] WARNING - __main__ - main.py:5580 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-07-01 11:50:48,113] INFO - __main__ - main.py:5586 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 PHASE INVESTIGATION:
[2025-07-01 11:50:48,113] INFO - __main__ - main.py:5587 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Retrieved from Firestore: 'conclusion_summary'
[2025-07-01 11:50:48,113] INFO - __main__ - main.py:5588 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-07-01 11:50:48,113] INFO - __main__ - main.py:5589 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Is first encounter: True
[2025-07-01 11:50:48,113] INFO - __main__ - main.py:5590 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Diagnostic completed: False
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:5596 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ Using stored phase from Firestore: 'conclusion_summary'
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:5610 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:5612 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Final phase for AI logic: conclusion_summary
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:5632 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:3829 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Diagnostic context validation passed
[2025-07-01 11:50:48,114] INFO - __main__ - main.py:3850 - DETERMINE_PHASE: Preserving advanced phase: 'conclusion_summary' - no backward transitions allowed
[2025-07-01 11:50:48,114] WARNING - __main__ - main.py:5720 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'conclusion_summary' for first encounter
[2025-07-01 11:50:48,115] INFO - __main__ - main.py:5741 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Skipping diagnostic context enhancement for non-diagnostic phase: conclusion_summary
[2025-07-01 11:50:48,115] DEBUG - __main__ - main.py:5748 - 🧪 DEBUG PHASE: current_phase_for_ai = 'conclusion_summary'
[2025-07-01 11:50:48,115] DEBUG - __main__ - main.py:5749 - 🧪 DEBUG PHASE: determined_phase = 'conclusion_summary'
[2025-07-01 11:50:48,115] INFO - __main__ - main.py:5755 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Robust context prepared successfully. Phase: conclusion_summary
[2025-07-01 11:50:48,115] DEBUG - __main__ - main.py:5756 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 11:50:48,115] WARNING - __main__ - main.py:5926 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖 AI PROMPT GENERATION:
[2025-07-01 11:50:48,116] WARNING - __main__ - main.py:5927 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - Current phase: conclusion_summary
[2025-07-01 11:50:48,116] WARNING - __main__ - main.py:5928 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - Student query: Yes...
[2025-07-01 11:50:48,116] WARNING - __main__ - main.py:5929 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 11:50:48,116] DEBUG - __main__ - main.py:5932 - 🤖 GENERATING AI PROMPT:
[2025-07-01 11:50:48,116] DEBUG - __main__ - main.py:5933 - 🤖   Phase: conclusion_summary
[2025-07-01 11:50:48,117] DEBUG - __main__ - main.py:5934 - 🤖   Query: Yes...
[2025-07-01 11:50:48,117] DEBUG - __main__ - main.py:5935 - 🤖   Student: Andrea
[2025-07-01 11:50:48,117] INFO - __main__ - main.py:6861 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🚀 [START] enhance_lesson_content invoked. Query: 'Yes...'
[2025-07-01 11:50:48,117] INFO - __main__ - main.py:6872 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 📋 [PHASE] Current lesson phase: 'conclusion_summary'
[2025-07-01 11:50:48,117] INFO - __main__ - main.py:6997 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🎯 CONCLUSION SUMMARY PHASE: Using enhanced handler...
[2025-07-01 11:50:48,119] INFO - __main__ - main.py:14990 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1][CONCLUSION_HANDLER] 🎉 [START] Handling conclusion: Generating student summary AND finalizing session data.
[2025-07-01 11:50:48,119] INFO - __main__ - main.py:14991 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1][CONCLUSION_HANDLER] 🎉 [START] User message: 'Yes'
[2025-07-01 11:50:48,122] ERROR - __main__ - main.py:15140 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1][CONCLUSION_HANDLER] Critical error in conclusion/finalization phase: name 'get_lesson_state_from_firestore' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py", line 15018, in handle_conclusion_summary_phase
    state_data = get_lesson_state_from_firestore(session_id)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_lesson_state_from_firestore' is not defined
[2025-07-01 11:50:48,249] INFO - __main__ - main.py:7006 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🎯 Enhanced conclusion summary handler completed -> completed
[2025-07-01 11:50:48,249] WARNING - __main__ - main.py:5957 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖 AI RESPONSE RECEIVED:
[2025-07-01 11:50:48,250] WARNING - __main__ - main.py:5958 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - Content length: 53 chars
[2025-07-01 11:50:48,250] WARNING - __main__ - main.py:5959 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - State updates: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 11:50:48,250] WARNING - __main__ - main.py:5960 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🤖   - Raw state block: None...
[2025-07-01 11:50:48,250] DEBUG - __main__ - main.py:5963 - 🤖 AI RESPONSE PROCESSED:
[2025-07-01 11:50:48,250] DEBUG - __main__ - main.py:5964 - 🤖   Content: Great job! You've completed this lesson successfully!...
[2025-07-01 11:50:48,250] DEBUG - __main__ - main.py:5965 - 🤖   State: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 11:50:48,250] INFO - __main__ - main.py:5991 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-07-01 11:50:48,251] INFO - __main__ - main.py:5992 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] CURRENT PHASE DETERMINATION: AI=completed, Session=conclusion_summary, Final=completed
[2025-07-01 11:50:48,528] INFO - __main__ - main.py:6041 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI processing completed in 0.41s
[2025-07-01 11:50:48,529] WARNING - __main__ - main.py:6052 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 STATE UPDATE VALIDATION: current_phase='conclusion_summary', new_phase='completed'
[2025-07-01 11:50:48,529] INFO - __main__ - main.py:4034 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI state update validation passed: conclusion_summary → completed
[2025-07-01 11:50:48,530] WARNING - __main__ - main.py:6061 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ STATE UPDATE VALIDATION PASSED
[2025-07-01 11:50:48,530] WARNING - __main__ - main.py:6066 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔄 PHASE TRANSITION: conclusion_summary → completed
[2025-07-01 11:50:48,531] WARNING - __main__ - main.py:6075 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-07-01 11:50:48,531] WARNING - __main__ - main.py:6076 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   1. Input phase: 'conclusion_summary'
[2025-07-01 11:50:48,531] WARNING - __main__ - main.py:6077 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-07-01 11:50:48,532] WARNING - __main__ - main.py:6078 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   3. AI state updates: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 11:50:48,532] WARNING - __main__ - main.py:6079 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍   4. Final phase to save: 'completed'
[2025-07-01 11:50:48,533] WARNING - __main__ - main.py:6082 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 💾 FINAL STATE APPLICATION:
[2025-07-01 11:50:48,533] WARNING - __main__ - main.py:6083 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 💾   - Current phase input: 'conclusion_summary'
[2025-07-01 11:50:48,533] WARNING - __main__ - main.py:6084 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 💾   - State updates from AI: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 11:50:48,534] WARNING - __main__ - main.py:6085 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 💾   - Final phase to save: 'completed'
[2025-07-01 11:50:48,534] WARNING - __main__ - main.py:6086 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 💾   - Phase change: True
[2025-07-01 11:50:48,534] INFO - __main__ - main.py:4066 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] DIAGNOSTIC_FLOW_METRICS:
[2025-07-01 11:50:48,535] INFO - __main__ - main.py:4067 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Phase transition: conclusion_summary -> completed
[2025-07-01 11:50:48,535] INFO - __main__ - main.py:4068 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Current level: 5
[2025-07-01 11:50:48,535] INFO - __main__ - main.py:4069 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Question index: 0
[2025-07-01 11:50:48,535] INFO - __main__ - main.py:4070 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   First encounter: True
[2025-07-01 11:50:48,536] INFO - __main__ - main.py:4075 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Answers collected: 5
[2025-07-01 11:50:48,536] INFO - __main__ - main.py:4076 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Levels failed: 0
[2025-07-01 11:50:48,536] INFO - __main__ - main.py:4034 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI state update validation passed: conclusion_summary → completed
[2025-07-01 11:50:48,536] INFO - __main__ - main.py:4080 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   State update valid: True
[2025-07-01 11:50:48,537] INFO - __main__ - main.py:4087 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1]   Diagnostic complete: False
[2025-07-01 11:50:48,537] WARNING - __main__ - main.py:6099 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
[2025-07-01 11:50:48,537] INFO - __main__ - main.py:6108 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-07-01 11:50:48,538] INFO - __main__ - main.py:6109 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] 🔍 DEBUG original teaching_interactions: 12
[2025-07-01 11:50:49,057] WARNING - __main__ - main.py:6154 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-07-01 11:50:49,058] WARNING - __main__ - main.py:6155 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Phase: completed
[2025-07-01 11:50:49,058] WARNING - __main__ - main.py:6156 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Probing Level: 5
[2025-07-01 11:50:49,059] WARNING - __main__ - main.py:6157 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Question Index: 0
[2025-07-01 11:50:49,059] WARNING - __main__ - main.py:6158 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Diagnostic Complete: False
[2025-07-01 11:50:49,059] WARNING - __main__ - main.py:6165 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Quiz Questions Saved: 10
[2025-07-01 11:50:49,060] WARNING - __main__ - main.py:6166 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Quiz Answers Saved: 10
[2025-07-01 11:50:49,060] WARNING - __main__ - main.py:6167 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Quiz Started: True
[2025-07-01 11:50:49,061] DEBUG - __main__ - main.py:6170 - 🔥 STATE SAVED - Session: session_35197f7f-9cfc-4675-8a84-badd21f85cb0, Phase: completed
[2025-07-01 11:50:49,061] DEBUG - __main__ - main.py:6171 - 🔥 QUIZ DATA - Questions: 10, Answers: 10
[2025-07-01 11:50:49,883] DEBUG - __main__ - main.py:6229 - ✅ SESSION UPDATED - ID: session_35197f7f-9cfc-4675-8a84-badd21f85cb0, Phase: completed
[2025-07-01 11:50:49,884] DEBUG - __main__ - main.py:6230 - ✅ INTERACTION LOGGED - Phase: conclusion_summary → completed
[2025-07-01 11:50:49,885] INFO - __main__ - main.py:6236 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ Updated existing session document: session_35197f7f-9cfc-4675-8a84-badd21f85cb0
[2025-07-01 11:50:49,885] WARNING - __main__ - main.py:6237 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅ SESSION UPDATE COMPLETE:
[2025-07-01 11:50:49,886] WARNING - __main__ - main.py:6238 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Session ID: session_35197f7f-9cfc-4675-8a84-badd21f85cb0
[2025-07-01 11:50:49,886] WARNING - __main__ - main.py:6239 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Phase transition: conclusion_summary → completed
[2025-07-01 11:50:49,887] WARNING - __main__ - main.py:6240 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] ✅   - Interaction logged successfully
[2025-07-01 11:50:49,887] INFO - __main__ - main.py:13140 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-07-01 11:50:49,887] DEBUG - __main__ - main.py:2822 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-07-01 11:50:49,888] DEBUG - __main__ - main.py:6295 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] No final assessment data found in AI response
[2025-07-01 11:50:49,888] DEBUG - __main__ - main.py:6323 - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
[2025-07-01 11:50:49,890] DEBUG - __main__ - main.py:6324 - 🔒   Current Phase: conclusion_summary
[2025-07-01 11:50:49,890] DEBUG - __main__ - main.py:6325 - 🔒   Final Phase: completed
[2025-07-01 11:50:49,890] DEBUG - __main__ - main.py:6326 - 🔒   Diagnostic Complete: False
[2025-07-01 11:50:49,891] DEBUG - __main__ - main.py:6327 - 🔒   Assigned Level: None
[2025-07-01 11:50:49,891] DEBUG - __main__ - main.py:6384 - 🎯 RESPONSE READY:
[2025-07-01 11:50:49,891] DEBUG - __main__ - main.py:6385 - 🎯   Session: session_35197f7f-9cfc-4675-8a84-badd21f85cb0
[2025-07-01 11:50:49,892] DEBUG - __main__ - main.py:6386 - 🎯   Phase: conclusion_summary → completed
[2025-07-01 11:50:49,892] DEBUG - __main__ - main.py:6387 - 🎯   Content: Great job! You've completed this lesson successful...
[2025-07-01 11:50:49,893] DEBUG - __main__ - main.py:6388 - 🎯   Request ID: 4fe2ac64-9940-4d5d-98ea-272de3d6abd1
[2025-07-01 11:50:49,893] INFO - __main__ - main.py:6394 - [4fe2ac64-9940-4d5d-98ea-272de3d6abd1] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-07-01 11:50:49,894] WARNING - __main__ - main.py:690 - High response time detected: 3.91s for enhance_content_api
