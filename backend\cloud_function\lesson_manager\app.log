[2025-07-01 13:22:38,179] INFO - main - main.py:695 - ================================================================================
[2025-07-01 13:22:38,180] INFO - main - main.py:696 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 13:22:38,180] INFO - main - main.py:697 - ================================================================================
[2025-07-01 13:22:38,181] INFO - main - main.py:698 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 13:22:38,181] INFO - main - main.py:699 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
[2025-07-01 13:22:38,181] INFO - main - main.py:700 - Log level: DEBUG
[2025-07-01 13:22:38,181] INFO - main - main.py:701 - ================================================================================
[2025-07-01 13:22:38,182] INFO - main - main.py:703 - Logging configuration complete with immediate console output
[2025-07-01 13:22:38,182] INFO - main - main.py:704 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 13:22:38,184] INFO - main - main.py:779 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 13:22:38,187] INFO - main - main.py:958 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 13:22:38,187] INFO - main - main.py:987 - Phase transition fixes imported successfully
[2025-07-01 13:22:38,197] INFO - main - main.py:3367 - Successfully imported utils functions
[2025-07-01 13:22:38,197] INFO - main - main.py:3375 - Successfully imported extract_ai_state functions
[2025-07-01 13:22:38,202] INFO - main - main.py:3825 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 13:22:38,203] INFO - unified_firebase_init - unified_firebase_init.py:65 - Firebase already initialized
[2025-07-01 13:22:38,203] INFO - main - main.py:3833 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 13:22:38,203] INFO - main - main.py:3923 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 13:22:38,234] INFO - main - main.py:1201 - Successfully imported timetable_generator functions
[2025-07-01 13:22:38,275] WARNING - auth_decorator - auth_decorator.py:56 - Could not fetch student name from Firestore: View function mapping is overwriting an existing endpoint function: static_files
[2025-07-01 13:22:38,276] INFO - auth_decorator - auth_decorator.py:160 - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Development Student
[2025-07-01 13:22:38,276] INFO - auth_decorator - auth_decorator.py:164 - [91d61886-259e-4efa-8892-5bfe2cdf038c][require_auth] Development auth: uid=andrea_ugono_33305, name=Development Student
[2025-07-01 13:22:38,277] INFO - __main__ - main.py:5289 - 
================================================================================
[2025-07-01 13:22:38,277] WARNING - __main__ - main.py:5289 - 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
[2025-07-01 13:22:38,278] WARNING - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-07-01 13:22:38,278] INFO - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-COM-003","content_to_enhance":"Yes","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","subject":"Computing","session_id":"session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0","chat_history":[{"role":"user","content":"It means planning out what the program will do and how it will work before you start coding.","timestamp":"2025-07-01T12:20:38.776Z"},{"role":"assistant","content":"**Question 9 of 10 (Short Answer)**...
[2025-07-01 13:22:38,279] INFO - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-COM-003', 'content_to_enhance': 'Yes', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Computing', 'session_id': 'session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', 'chat_history': [{'role': 'user', 'content': 'It means planning out what the program will do and how it will work before you start coding.', 'timestamp': '2025-07-01T12:20:38.776Z'}, {'role': 'assistant', 'content': "**Question 9 of 10 (Short Answer)**\n\n*Why is it important to 'debug' a program?*", 'timestamp': '2025-07-01T12:20:44.246Z'}, {'role': 'user', 'content': "So it works correctly and doesn't have mistakes.", 'timestamp': '2025-07-01T12:21:26.569Z'}, {'role': 'assistant', 'content': "**Question 10 of 10 (Short Answer)**\n\n*What is the difference between 'designing' and 'writing' a program?*", 'timestamp': '2025-07-01T12:21:32.385Z'}, {'role': 'user', 'content': 'Designing is the plan, writing is actually making the plan with code.', 'timestamp': '2025-07-01T12:22:15.083Z'}, {'role': 'assistant', 'content': "🎉 Excellent work, Andrea! You've completed the quiz. Let me compile your results.", 'timestamp': '2025-07-01T12:22:20.781Z'}, {'role': 'user', 'content': 'ok', 'timestamp': '2025-07-01T12:22:26.152Z'}, {'role': 'assistant', 'content': "🎯 **Quiz Results for Advanced Coding**\n\nOutstanding! 🌟 Student! Here's your detailed performance analysis:\n- Score: 90% (9/10 correct)\n- You've successfully completed the quiz portion of the lesson!\n\nReady to wrap up with a summary of everything you've learned?", 'timestamp': '2025-07-01T12:22:32.694Z'}]}
[2025-07-01 13:22:38,280] INFO - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍  - Session ID from payload: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 13:22:38,280] INFO - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍  - Student ID from payload: andrea_ugono_33305
[2025-07-01 13:22:38,281] INFO - __main__ - main.py:5289 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍  - Lesson Ref from payload: P5-COM-003
[2025-07-01 13:22:38,281] DEBUG - __main__ - main.py:5346 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', lesson_ref='P5-COM-003'
[2025-07-01 13:22:38,281] INFO - __main__ - main.py:5347 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Parsed Params: student_id='andrea_ugono_33305', session_id='session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', lesson_ref='P5-COM-003'
[2025-07-01 13:22:38,656] INFO - __main__ - main.py:4637 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-07-01 13:22:38,657] DEBUG - __main__ - main.py:737 - Cache hit for fetch_lesson_data
[2025-07-01 13:22:38,657] INFO - __main__ - main.py:5397 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ All required fields present after lesson content parsing and mapping
[2025-07-01 13:22:38,657] INFO - __main__ - main.py:5436 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Attempting to infer module. GS Subject Slug: 'computing'.
[2025-07-01 13:22:38,657] INFO - __main__ - main.py:2418 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Inferring module for subject 'computing', lesson 'Advanced Coding Techniques'.
[2025-07-01 13:22:39,589] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'algorithms_and_computational_thinking' ('Algorithms & Computational Thinking')
[2025-07-01 13:22:39,589] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'computer_systems_and_networks' ('Computer Systems & Networks')
[2025-07-01 13:22:39,590] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'cyber_security' ('Cyber-Security')
[2025-07-01 13:22:39,590] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'data_representation_and_databases' ('Data Representation & Databases')
[2025-07-01 13:22:39,590] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'digital_literacy_and_e_safety' ('Digital Literacy & E-Safety')
[2025-07-01 13:22:39,591] INFO - __main__ - main.py:2477 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Loaded metadata for module 'programming_and_software_development' ('Programming & Software Development')
[2025-07-01 13:22:39,591] INFO - __main__ - main.py:2546 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Starting module inference for subject 'computing' with 6 module options
[2025-07-01 13:22:39,591] DEBUG - __main__ - main.py:2560 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Sample modules available (first 3):
- algorithms_and_computational_thinking: Algorithms & Computational Thinking
- computer_systems_and_networks: Computer Systems & Networks
- cyber_security: Cyber-Security
[2025-07-01 13:22:39,592] DEBUG - __main__ - main.py:2563 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Advanced Coding Techniques. Topic: Advanced Coding. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug; programs; solve; Incorporate; variables; functions; codin...
[2025-07-01 13:22:39,592] DEBUG - __main__ - main.py:2564 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference Lesson Summary (first 300 chars): Lesson Title: Advanced Coding Techniques. Topic: Advanced Coding. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug; programs; solve; Incorporate; variables; functions; coding;...
[2025-07-01 13:22:39,592] DEBUG - __main__ - main.py:2565 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference Module Options (first 200 chars): 1. Slug: "algorithms_and_computational_thinking", Name: "Algorithms & Computational Thinking", Description: "UK strand: Computational Thinking; mirrors GCSE ‘Algorithmic Thinking’...."
2. Slug: "compu...
[2025-07-01 13:22:39,593] INFO - __main__ - main.py:2569 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Calling Gemini API for module inference...
[2025-07-01 13:22:40,042] INFO - __main__ - main.py:2579 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Gemini API call completed in 0.45s. Raw response: 'programming_and_software_development'
[2025-07-01 13:22:40,043] DEBUG - __main__ - main.py:2601 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Cleaned slug: 'programming_and_software_development'
[2025-07-01 13:22:40,043] INFO - __main__ - main.py:2606 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI Inference: Successfully matched module by slug. Chosen: 'programming_and_software_development' (Programming & Software Development)
[2025-07-01 13:22:40,043] INFO - __main__ - main.py:5470 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Successfully inferred module ID via AI: programming_and_software_development
[2025-07-01 13:22:40,043] INFO - __main__ - main.py:5507 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Effective module name for prompt context: 'Programming & Software Development' (Module ID: programming_and_software_development)
[2025-07-01 13:22:40,394] INFO - __main__ - main.py:2136 - No prior student performance document found for Topic: computing_Primary 5_computing_programming_and_software_development
[2025-07-01 13:22:41,328] DEBUG - __main__ - main.py:5523 - 🔍 SESSION STATE RETRIEVAL:
[2025-07-01 13:22:41,329] DEBUG - __main__ - main.py:5524 - 🔍   - Session ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 13:22:41,329] DEBUG - __main__ - main.py:5525 - 🔍   - Document Exists: True
[2025-07-01 13:22:41,330] DEBUG - __main__ - main.py:5526 - 🔍   - Current Phase: conclusion_summary
[2025-07-01 13:22:41,330] DEBUG - __main__ - main.py:5527 - 🔍   - Probing Level: 5
[2025-07-01 13:22:41,330] DEBUG - __main__ - main.py:5528 - 🔍   - Question Index: 0
[2025-07-01 13:22:41,330] WARNING - __main__ - main.py:5534 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 SESSION STATE DEBUG:
[2025-07-01 13:22:41,330] WARNING - __main__ - main.py:5535 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   - Session exists: True
[2025-07-01 13:22:41,331] WARNING - __main__ - main.py:5536 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   - Current phase: conclusion_summary
[2025-07-01 13:22:41,331] WARNING - __main__ - main.py:5537 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'teaching_complete', 'assigned_level_for_teaching', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-07-01 13:22:41,331] DEBUG - __main__ - main.py:5556 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
[2025-07-01 13:22:41,332] DEBUG - __main__ - main.py:5557 - [91d61886-259e-4efa-8892-5bfe2cdf038c] � Retrieved Phase: 'conclusion_summary'
[2025-07-01 13:22:41,332] DEBUG - __main__ - main.py:5558 - [91d61886-259e-4efa-8892-5bfe2cdf038c] � Diagnostic Completed: False
[2025-07-01 13:22:41,332] DEBUG - __main__ - main.py:5559 - [91d61886-259e-4efa-8892-5bfe2cdf038c] � Assigned Level: None
[2025-07-01 13:22:41,333] WARNING - __main__ - main.py:5560 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔒 STATE PROTECTION: phase='conclusion_summary', diagnostic_done=False, level=None
[2025-07-01 13:22:41,333] INFO - __main__ - main.py:5604 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ State protection not triggered (diagnostic=False, level=None)
[2025-07-01 13:22:41,333] INFO - __main__ - main.py:5605 - [91d61886-259e-4efa-8892-5bfe2cdf038c] State protection not triggered
[2025-07-01 13:22:41,333] INFO - __main__ - main.py:5646 - [91d61886-259e-4efa-8892-5bfe2cdf038c] �🔍 FIRST ENCOUNTER LOGIC:
[2025-07-01 13:22:41,334] INFO - __main__ - main.py:5647 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   assigned_level_for_teaching (session): None
[2025-07-01 13:22:41,334] INFO - __main__ - main.py:5648 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   latest_assessed_level (profile): None
[2025-07-01 13:22:41,334] INFO - __main__ - main.py:5649 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   teaching_level_for_returning_student: None
[2025-07-01 13:22:41,334] INFO - __main__ - main.py:5650 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   has_completed_diagnostic_before: False
[2025-07-01 13:22:41,335] INFO - __main__ - main.py:5651 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   is_first_encounter_for_module: True
[2025-07-01 13:22:41,335] WARNING - __main__ - main.py:5656 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-07-01 13:22:41,335] INFO - __main__ - main.py:5662 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 PHASE INVESTIGATION:
[2025-07-01 13:22:41,335] INFO - __main__ - main.py:5663 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Retrieved from Firestore: 'conclusion_summary'
[2025-07-01 13:22:41,336] INFO - __main__ - main.py:5664 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-07-01 13:22:41,336] INFO - __main__ - main.py:5665 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Is first encounter: True
[2025-07-01 13:22:41,336] INFO - __main__ - main.py:5666 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Diagnostic completed: False
[2025-07-01 13:22:41,337] INFO - __main__ - main.py:5672 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ Using stored phase from Firestore: 'conclusion_summary'
[2025-07-01 13:22:41,337] INFO - __main__ - main.py:5686 - [91d61886-259e-4efa-8892-5bfe2cdf038c] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-07-01 13:22:41,337] INFO - __main__ - main.py:5688 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Final phase for AI logic: conclusion_summary
[2025-07-01 13:22:41,337] INFO - __main__ - main.py:5708 - [91d61886-259e-4efa-8892-5bfe2cdf038c] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-07-01 13:22:41,338] INFO - __main__ - main.py:3905 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Diagnostic context validation passed
[2025-07-01 13:22:41,338] INFO - __main__ - main.py:3926 - DETERMINE_PHASE: Preserving advanced phase: 'conclusion_summary' - no backward transitions allowed
[2025-07-01 13:22:41,338] WARNING - __main__ - main.py:5796 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'conclusion_summary' for first encounter
[2025-07-01 13:22:41,339] INFO - __main__ - main.py:5817 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Skipping diagnostic context enhancement for non-diagnostic phase: conclusion_summary
[2025-07-01 13:22:41,339] DEBUG - __main__ - main.py:5824 - 🧪 DEBUG PHASE: current_phase_for_ai = 'conclusion_summary'
[2025-07-01 13:22:41,339] DEBUG - __main__ - main.py:5825 - 🧪 DEBUG PHASE: determined_phase = 'conclusion_summary'
[2025-07-01 13:22:41,339] INFO - __main__ - main.py:5831 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Robust context prepared successfully. Phase: conclusion_summary
[2025-07-01 13:22:41,340] DEBUG - __main__ - main.py:5832 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 13:22:41,340] WARNING - __main__ - main.py:6002 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖 AI PROMPT GENERATION:
[2025-07-01 13:22:41,340] WARNING - __main__ - main.py:6003 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - Current phase: conclusion_summary
[2025-07-01 13:22:41,340] WARNING - __main__ - main.py:6004 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - Student query: Yes...
[2025-07-01 13:22:41,340] WARNING - __main__ - main.py:6005 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 13:22:41,341] DEBUG - __main__ - main.py:6008 - 🤖 GENERATING AI PROMPT:
[2025-07-01 13:22:41,341] DEBUG - __main__ - main.py:6009 - 🤖   Phase: conclusion_summary
[2025-07-01 13:22:41,341] DEBUG - __main__ - main.py:6010 - 🤖   Query: Yes...
[2025-07-01 13:22:41,341] DEBUG - __main__ - main.py:6011 - 🤖   Student: Andrea
[2025-07-01 13:22:41,341] INFO - __main__ - main.py:6937 - [91d61886-259e-4efa-8892-5bfe2cdf038c] enhance_lesson_content invoked. Query: 'Yes...'
[2025-07-01 13:22:41,342] INFO - __main__ - main.py:7028 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🎯 CONCLUSION SUMMARY PHASE: Using enhanced handler...
[2025-07-01 13:22:41,342] INFO - __main__ - main.py:14724 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Teaching level search: assigned_level_for_teaching=None, current_session_working_level=None
[2025-07-01 13:22:41,342] INFO - __main__ - main.py:14745 - [91d61886-259e-4efa-8892-5bfe2cdf038c] No teaching level found in lesson context
[2025-07-01 13:22:41,343] INFO - __main__ - main.py:11027 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Generated 4 homework assignments for 90% performance
[2025-07-01 13:22:41,343] ERROR - __main__ - main.py:14881 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Cannot save lesson summary - missing required data
[2025-07-01 13:22:41,344] ERROR - __main__ - main.py:11410 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Missing session_id or student_id for completion activities
[2025-07-01 13:22:41,344] INFO - __main__ - main.py:14859 - [REQ 91d61886-259e-4efa-8892-5bfe2cdf038c] Conclusion summary phase completed (Performance: 90%) -> completed
[2025-07-01 13:22:41,344] INFO - __main__ - main.py:7037 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🎯 Enhanced conclusion summary handler completed -> completed
[2025-07-01 13:22:41,344] WARNING - __main__ - main.py:6033 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖 AI RESPONSE RECEIVED:
[2025-07-01 13:22:41,345] WARNING - __main__ - main.py:6034 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - Content length: 1483 chars
[2025-07-01 13:22:41,345] WARNING - __main__ - main.py:6035 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - State updates: {'new_phase': 'completed', 'lesson_complete': True}
[2025-07-01 13:22:41,345] WARNING - __main__ - main.py:6036 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🤖   - Raw state block: None...
[2025-07-01 13:22:41,346] DEBUG - __main__ - main.py:6039 - 🤖 AI RESPONSE PROCESSED:
[2025-07-01 13:22:41,346] DEBUG - __main__ - main.py:6040 - 🤖   Content: 🎓 **Lesson Summary: Advanced Coding**

**Successfully Completed! 🎉** Well done, Student! Here's your...
[2025-07-01 13:22:41,346] DEBUG - __main__ - main.py:6041 - 🤖   State: {'new_phase': 'completed', 'lesson_complete': True}
[2025-07-01 13:22:41,347] INFO - __main__ - main.py:6067 - [91d61886-259e-4efa-8892-5bfe2cdf038c] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-07-01 13:22:41,347] INFO - __main__ - main.py:6068 - [91d61886-259e-4efa-8892-5bfe2cdf038c] CURRENT PHASE DETERMINATION: AI=completed, Session=conclusion_summary, Final=completed
[2025-07-01 13:22:41,630] INFO - __main__ - main.py:6117 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI processing completed in 0.29s
[2025-07-01 13:22:41,630] WARNING - __main__ - main.py:6128 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 STATE UPDATE VALIDATION: current_phase='conclusion_summary', new_phase='completed'
[2025-07-01 13:22:41,630] INFO - __main__ - main.py:4110 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI state update validation passed: conclusion_summary → completed
[2025-07-01 13:22:41,630] WARNING - __main__ - main.py:6137 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ STATE UPDATE VALIDATION PASSED
[2025-07-01 13:22:41,631] WARNING - __main__ - main.py:6142 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔄 PHASE TRANSITION: conclusion_summary → completed
[2025-07-01 13:22:41,631] WARNING - __main__ - main.py:6151 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-07-01 13:22:41,631] WARNING - __main__ - main.py:6152 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   1. Input phase: 'conclusion_summary'
[2025-07-01 13:22:41,632] WARNING - __main__ - main.py:6153 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-07-01 13:22:41,632] WARNING - __main__ - main.py:6154 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   3. AI state updates: {'new_phase': 'completed', 'lesson_complete': True}
[2025-07-01 13:22:41,633] WARNING - __main__ - main.py:6155 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍   4. Final phase to save: 'completed'
[2025-07-01 13:22:41,633] WARNING - __main__ - main.py:6158 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 💾 FINAL STATE APPLICATION:
[2025-07-01 13:22:41,634] WARNING - __main__ - main.py:6159 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 💾   - Current phase input: 'conclusion_summary'
[2025-07-01 13:22:41,634] WARNING - __main__ - main.py:6160 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 💾   - State updates from AI: {'new_phase': 'completed', 'lesson_complete': True}
[2025-07-01 13:22:41,634] WARNING - __main__ - main.py:6161 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 💾   - Final phase to save: 'completed'
[2025-07-01 13:22:41,635] WARNING - __main__ - main.py:6162 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 💾   - Phase change: True
[2025-07-01 13:22:41,635] INFO - __main__ - main.py:4142 - [91d61886-259e-4efa-8892-5bfe2cdf038c] DIAGNOSTIC_FLOW_METRICS:
[2025-07-01 13:22:41,635] INFO - __main__ - main.py:4143 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Phase transition: conclusion_summary -> completed
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4144 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Current level: 5
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4145 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Question index: 0
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4146 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   First encounter: True
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4151 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Answers collected: 5
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4152 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Levels failed: 0
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4110 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI state update validation passed: conclusion_summary → completed
[2025-07-01 13:22:41,636] INFO - __main__ - main.py:4156 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   State update valid: True
[2025-07-01 13:22:41,637] INFO - __main__ - main.py:4163 - [91d61886-259e-4efa-8892-5bfe2cdf038c]   Diagnostic complete: False
[2025-07-01 13:22:41,637] WARNING - __main__ - main.py:6175 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
[2025-07-01 13:22:41,637] INFO - __main__ - main.py:6184 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-07-01 13:22:41,637] INFO - __main__ - main.py:6185 - [91d61886-259e-4efa-8892-5bfe2cdf038c] 🔍 DEBUG original teaching_interactions: 8
[2025-07-01 13:22:42,626] WARNING - __main__ - main.py:6230 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-07-01 13:22:42,626] WARNING - __main__ - main.py:6231 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Phase: completed
[2025-07-01 13:22:42,627] WARNING - __main__ - main.py:6232 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Probing Level: 5
[2025-07-01 13:22:42,627] WARNING - __main__ - main.py:6233 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Question Index: 0
[2025-07-01 13:22:42,627] WARNING - __main__ - main.py:6234 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Diagnostic Complete: False
[2025-07-01 13:22:42,627] WARNING - __main__ - main.py:6241 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Quiz Questions Saved: 10
[2025-07-01 13:22:42,628] WARNING - __main__ - main.py:6242 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Quiz Answers Saved: 10
[2025-07-01 13:22:42,628] WARNING - __main__ - main.py:6243 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Quiz Started: True
[2025-07-01 13:22:42,628] DEBUG - __main__ - main.py:6246 - 🔥 STATE SAVED - Session: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0, Phase: completed
[2025-07-01 13:22:42,628] DEBUG - __main__ - main.py:6247 - 🔥 QUIZ DATA - Questions: 10, Answers: 10
[2025-07-01 13:22:44,166] DEBUG - __main__ - main.py:6305 - ✅ SESSION UPDATED - ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0, Phase: completed
[2025-07-01 13:22:44,167] DEBUG - __main__ - main.py:6306 - ✅ INTERACTION LOGGED - Phase: conclusion_summary → completed
[2025-07-01 13:22:44,167] INFO - __main__ - main.py:6312 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ Updated existing session document: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 13:22:44,167] WARNING - __main__ - main.py:6313 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅ SESSION UPDATE COMPLETE:
[2025-07-01 13:22:44,167] WARNING - __main__ - main.py:6314 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Session ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 13:22:44,168] WARNING - __main__ - main.py:6315 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Phase transition: conclusion_summary → completed
[2025-07-01 13:22:44,168] WARNING - __main__ - main.py:6316 - [91d61886-259e-4efa-8892-5bfe2cdf038c] ✅   - Interaction logged successfully
[2025-07-01 13:22:44,168] INFO - __main__ - main.py:12921 - [91d61886-259e-4efa-8892-5bfe2cdf038c] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-07-01 13:22:44,168] DEBUG - __main__ - main.py:2898 - [91d61886-259e-4efa-8892-5bfe2cdf038c] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-07-01 13:22:44,169] DEBUG - __main__ - main.py:6371 - [91d61886-259e-4efa-8892-5bfe2cdf038c] No final assessment data found in AI response
[2025-07-01 13:22:44,169] DEBUG - __main__ - main.py:6399 - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
[2025-07-01 13:22:44,169] DEBUG - __main__ - main.py:6400 - 🔒   Current Phase: conclusion_summary
[2025-07-01 13:22:44,169] DEBUG - __main__ - main.py:6401 - 🔒   Final Phase: completed
[2025-07-01 13:22:44,170] DEBUG - __main__ - main.py:6402 - 🔒   Diagnostic Complete: False
[2025-07-01 13:22:44,170] DEBUG - __main__ - main.py:6403 - 🔒   Assigned Level: None
[2025-07-01 13:22:44,170] DEBUG - __main__ - main.py:6460 - 🎯 RESPONSE READY:
[2025-07-01 13:22:44,171] DEBUG - __main__ - main.py:6461 - 🎯   Session: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 13:22:44,171] DEBUG - __main__ - main.py:6462 - 🎯   Phase: conclusion_summary → completed
[2025-07-01 13:22:44,171] DEBUG - __main__ - main.py:6463 - 🎯   Content: 🎓 **Lesson Summary: Advanced Coding**

**Successfu...
[2025-07-01 13:22:44,171] DEBUG - __main__ - main.py:6464 - 🎯   Request ID: 91d61886-259e-4efa-8892-5bfe2cdf038c
[2025-07-01 13:22:44,172] INFO - __main__ - main.py:6470 - [91d61886-259e-4efa-8892-5bfe2cdf038c] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-07-01 13:22:44,173] WARNING - __main__ - main.py:766 - High response time detected: 5.90s for enhance_content_api
