[2025-07-01 12:53:48,406] INFO - main - main.py:695 - ================================================================================
[2025-07-01 12:53:48,408] INFO - main - main.py:696 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 12:53:48,409] INFO - main - main.py:697 - ================================================================================
[2025-07-01 12:53:48,409] INFO - main - main.py:698 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 12:53:48,409] INFO - main - main.py:699 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
[2025-07-01 12:53:48,410] INFO - main - main.py:700 - Log level: DEBUG
[2025-07-01 12:53:48,410] INFO - main - main.py:701 - ================================================================================
[2025-07-01 12:53:48,410] INFO - main - main.py:703 - Logging configuration complete with immediate console output
[2025-07-01 12:53:48,411] INFO - main - main.py:704 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 12:53:48,413] INFO - main - main.py:779 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 12:53:48,417] INFO - main - main.py:958 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 12:53:48,417] INFO - main - main.py:987 - Phase transition fixes imported successfully
[2025-07-01 12:53:48,426] INFO - main - main.py:3284 - Successfully imported utils functions
[2025-07-01 12:53:48,426] INFO - main - main.py:3292 - Successfully imported extract_ai_state functions
[2025-07-01 12:53:48,432] INFO - main - main.py:3742 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 12:53:48,432] INFO - unified_firebase_init - unified_firebase_init.py:65 - Firebase already initialized
[2025-07-01 12:53:48,433] INFO - main - main.py:3750 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 12:53:48,433] INFO - main - main.py:3840 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 12:53:48,469] INFO - main - main.py:1128 - Successfully imported timetable_generator functions
[2025-07-01 12:53:48,490] WARNING - auth_decorator - auth_decorator.py:56 - Could not fetch student name from Firestore: View function mapping is overwriting an existing endpoint function: static_files
[2025-07-01 12:53:48,492] INFO - auth_decorator - auth_decorator.py:160 - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Development Student
[2025-07-01 12:53:48,492] INFO - auth_decorator - auth_decorator.py:164 - [5e88d14e-9912-46f5-8e54-12d602013b9f][require_auth] Development auth: uid=andrea_ugono_33305, name=Development Student
[2025-07-01 12:53:48,492] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-07-01 12:53:48,494] INFO - __main__ - main.py:5289 - 
================================================================================
[2025-07-01 12:53:48,494] WARNING - __main__ - main.py:5289 - 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
[2025-07-01 12:53:48,494] WARNING - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-07-01 12:53:48,495] INFO - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-COM-003","content_to_enhance":"I don't know","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","subject":"Computing","session_id":"session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0","chat_history":[{"role":"user","content":"all of it","timestamp":"2025-07-01T11:52:35.407Z"},{"role":"assistant","content":"That's great you're ready to dive into Advanced Coding, Andrea! We'll be exploring how code works, building c...
[2025-07-01 12:53:48,495] INFO - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-COM-003', 'content_to_enhance': "I don't know", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Computing', 'session_id': 'session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', 'chat_history': [{'role': 'user', 'content': 'all of it', 'timestamp': '2025-07-01T11:52:35.407Z'}, {'role': 'assistant', 'content': "That's great you're ready to dive into Advanced Coding, Andrea! We'll be exploring how code works, building cool things, and learning about important building blocks like variables and functions. Let's start with our first question to get a feel for what you already know about Advanced Coding. Imagine you're designing a simple game where a character can jump. How might you use a **variable** to keep track of how high the character can jump?", 'timestamp': '2025-07-01T11:52:40.802Z'}, {'role': 'user', 'content': "I don't know", 'timestamp': '2025-07-01T11:52:52.896Z'}, {'role': 'assistant', 'content': "It's okay, Andrea! Sometimes new concepts can be a little tricky at first. Let's break down Advanced Coding together. Think about a video game you play. When you're playing, the game needs to remember things, right? Like your score, or how many lives you have left, or what level you're on. In coding, we use **variables** for this. A variable is like a container that can hold a piece of information, and we can change that information as the program runs. For example, in a game, we might have a variable called `score` which starts at 0. Every time you collect a coin, the game updates the `score` variable by adding points to it. So, the variable `score` changes its value. Does that idea of a variable as a changeable container for information make sense? We'll explore how these variables are used in more complex ways as we go!", 'timestamp': '2025-07-01T11:52:59.728Z'}, {'role': 'user', 'content': 'Yes it does', 'timestamp': '2025-07-01T11:53:13.410Z'}, {'role': 'assistant', 'content': "That's great to hear, Andrea! Now that we've confirmed that, let's dive into our first question to understand your current level with Advanced Coding. Think about a video game you enjoy. If you wanted to make a character in that game remember its health points, what programming concept would you use to store that number?", 'timestamp': '2025-07-01T11:53:20.256Z'}, {'role': 'user', 'content': "I don't know", 'timestamp': '2025-07-01T11:53:31.441Z'}, {'role': 'assistant', 'content': 'It\'s okay not to know, Andrea! That\'s what I\'m here for. We\'ll figure it out together. Let\'s think about **Variables** in Advanced Coding. Imagine you\'re building a game. A variable is like a special box where you can store information that might change as the game runs. For example, you could have a variable called `score` that starts at 0. Every time you collect a coin, the `score` variable would increase. Now, let\'s consider **Functions**. A function is like a mini-program or a set of instructions that you can use over and over again. Think about a "jump" button in a game. When you press it, a function called `jump()` runs, and it tells the character how to move upwards. Does this initial explanation of variables and functions make sense for now?', 'timestamp': '2025-07-01T11:53:38.035Z'}]}
[2025-07-01 12:53:48,496] INFO - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍  - Session ID from payload: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 12:53:48,496] INFO - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍  - Student ID from payload: andrea_ugono_33305
[2025-07-01 12:53:48,496] INFO - __main__ - main.py:5289 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍  - Lesson Ref from payload: P5-COM-003
[2025-07-01 12:53:48,497] DEBUG - __main__ - main.py:5346 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', lesson_ref='P5-COM-003'
[2025-07-01 12:53:48,497] INFO - __main__ - main.py:5347 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Parsed Params: student_id='andrea_ugono_33305', session_id='session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0', lesson_ref='P5-COM-003'
[2025-07-01 12:53:48,812] INFO - __main__ - main.py:4637 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-07-01 12:53:48,813] DEBUG - __main__ - main.py:737 - Cache hit for fetch_lesson_data
[2025-07-01 12:53:48,813] INFO - __main__ - main.py:5397 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ All required fields present after lesson content parsing and mapping
[2025-07-01 12:53:48,814] INFO - __main__ - main.py:5436 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Attempting to infer module. GS Subject Slug: 'computing'.
[2025-07-01 12:53:48,814] INFO - __main__ - main.py:2418 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Inferring module for subject 'computing', lesson 'Advanced Coding Techniques'.
[2025-07-01 12:53:49,454] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'algorithms_and_computational_thinking' ('Algorithms & Computational Thinking')
[2025-07-01 12:53:49,454] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'computer_systems_and_networks' ('Computer Systems & Networks')
[2025-07-01 12:53:49,455] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'cyber_security' ('Cyber-Security')
[2025-07-01 12:53:49,455] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'data_representation_and_databases' ('Data Representation & Databases')
[2025-07-01 12:53:49,456] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'digital_literacy_and_e_safety' ('Digital Literacy & E-Safety')
[2025-07-01 12:53:49,456] INFO - __main__ - main.py:2477 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Loaded metadata for module 'programming_and_software_development' ('Programming & Software Development')
[2025-07-01 12:53:49,457] INFO - __main__ - main.py:2546 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Starting module inference for subject 'computing' with 6 module options
[2025-07-01 12:53:49,458] DEBUG - __main__ - main.py:2560 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Sample modules available (first 3):
- algorithms_and_computational_thinking: Algorithms & Computational Thinking
- computer_systems_and_networks: Computer Systems & Networks
- cyber_security: Cyber-Security
[2025-07-01 12:53:49,459] DEBUG - __main__ - main.py:2563 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Advanced Coding Techniques. Topic: Advanced Coding. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug; programs; solve; Incorporate; variables; functions; codin...
[2025-07-01 12:53:49,460] DEBUG - __main__ - main.py:2564 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference Lesson Summary (first 300 chars): Lesson Title: Advanced Coding Techniques. Topic: Advanced Coding. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug; programs; solve; Incorporate; variables; functions; coding;...
[2025-07-01 12:53:49,461] DEBUG - __main__ - main.py:2565 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference Module Options (first 200 chars): 1. Slug: "algorithms_and_computational_thinking", Name: "Algorithms & Computational Thinking", Description: "UK strand: Computational Thinking; mirrors GCSE ‘Algorithmic Thinking’...."
2. Slug: "compu...
[2025-07-01 12:53:49,461] INFO - __main__ - main.py:2569 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Calling Gemini API for module inference...
[2025-07-01 12:53:50,170] INFO - __main__ - main.py:2579 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Gemini API call completed in 0.71s. Raw response: 'programming_and_software_development'
[2025-07-01 12:53:50,170] DEBUG - __main__ - main.py:2601 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Cleaned slug: 'programming_and_software_development'
[2025-07-01 12:53:50,171] INFO - __main__ - main.py:2606 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI Inference: Successfully matched module by slug. Chosen: 'programming_and_software_development' (Programming & Software Development)
[2025-07-01 12:53:50,171] INFO - __main__ - main.py:5470 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Successfully inferred module ID via AI: programming_and_software_development
[2025-07-01 12:53:50,171] INFO - __main__ - main.py:5507 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Effective module name for prompt context: 'Programming & Software Development' (Module ID: programming_and_software_development)
[2025-07-01 12:53:50,578] INFO - __main__ - main.py:2136 - No prior student performance document found for Topic: computing_Primary 5_computing_programming_and_software_development
[2025-07-01 12:53:50,974] DEBUG - __main__ - main.py:5523 - 🔍 SESSION STATE RETRIEVAL:
[2025-07-01 12:53:50,975] DEBUG - __main__ - main.py:5524 - 🔍   - Session ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 12:53:50,975] DEBUG - __main__ - main.py:5525 - 🔍   - Document Exists: True
[2025-07-01 12:53:50,976] DEBUG - __main__ - main.py:5526 - 🔍   - Current Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,977] DEBUG - __main__ - main.py:5527 - 🔍   - Probing Level: 5
[2025-07-01 12:53:50,977] DEBUG - __main__ - main.py:5528 - 🔍   - Question Index: 5
[2025-07-01 12:53:50,978] WARNING - __main__ - main.py:5534 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 SESSION STATE DEBUG:
[2025-07-01 12:53:50,979] WARNING - __main__ - main.py:5535 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Session exists: True
[2025-07-01 12:53:50,980] WARNING - __main__ - main.py:5536 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Current phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,980] WARNING - __main__ - main.py:5537 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'student_name', 'lesson_context_snapshot', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-07-01 12:53:50,981] DEBUG - __main__ - main.py:5556 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
[2025-07-01 12:53:50,982] DEBUG - __main__ - main.py:5557 - [5e88d14e-9912-46f5-8e54-12d602013b9f] � Retrieved Phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,982] DEBUG - __main__ - main.py:5558 - [5e88d14e-9912-46f5-8e54-12d602013b9f] � Diagnostic Completed: False
[2025-07-01 12:53:50,983] DEBUG - __main__ - main.py:5559 - [5e88d14e-9912-46f5-8e54-12d602013b9f] � Assigned Level: None
[2025-07-01 12:53:50,983] WARNING - __main__ - main.py:5560 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔒 STATE PROTECTION: phase='diagnostic_probing_L5_eval_q5_decide_level', diagnostic_done=False, level=None
[2025-07-01 12:53:50,984] INFO - __main__ - main.py:5604 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ State protection not triggered (diagnostic=False, level=None)
[2025-07-01 12:53:50,985] INFO - __main__ - main.py:5605 - [5e88d14e-9912-46f5-8e54-12d602013b9f] State protection not triggered
[2025-07-01 12:53:50,985] INFO - __main__ - main.py:5646 - [5e88d14e-9912-46f5-8e54-12d602013b9f] �🔍 FIRST ENCOUNTER LOGIC:
[2025-07-01 12:53:50,986] INFO - __main__ - main.py:5647 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   assigned_level_for_teaching (session): None
[2025-07-01 12:53:50,986] INFO - __main__ - main.py:5648 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   latest_assessed_level (profile): None
[2025-07-01 12:53:50,986] INFO - __main__ - main.py:5649 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   teaching_level_for_returning_student: None
[2025-07-01 12:53:50,987] INFO - __main__ - main.py:5650 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   has_completed_diagnostic_before: False
[2025-07-01 12:53:50,987] INFO - __main__ - main.py:5651 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   is_first_encounter_for_module: True
[2025-07-01 12:53:50,987] WARNING - __main__ - main.py:5656 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-07-01 12:53:50,988] INFO - __main__ - main.py:5662 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 PHASE INVESTIGATION:
[2025-07-01 12:53:50,989] INFO - __main__ - main.py:5663 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,989] INFO - __main__ - main.py:5664 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-07-01 12:53:50,990] INFO - __main__ - main.py:5665 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Is first encounter: True
[2025-07-01 12:53:50,991] INFO - __main__ - main.py:5666 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Diagnostic completed: False
[2025-07-01 12:53:50,991] INFO - __main__ - main.py:5672 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,991] INFO - __main__ - main.py:5686 - [5e88d14e-9912-46f5-8e54-12d602013b9f] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-07-01 12:53:50,992] INFO - __main__ - main.py:5688 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Final phase for AI logic: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,993] INFO - __main__ - main.py:5702 - [5e88d14e-9912-46f5-8e54-12d602013b9f] NEW SESSION: Using phase-based question index 4 from phase diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,993] INFO - __main__ - main.py:3905 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Diagnostic context validation passed
[2025-07-01 12:53:50,994] INFO - __main__ - main.py:3938 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,995] WARNING - __main__ - main.py:5796 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q5_decide_level' for first encounter
[2025-07-01 12:53:50,995] INFO - __main__ - main.py:4015 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Enhanced diagnostic context with 45 fields
[2025-07-01 12:53:50,996] INFO - __main__ - main.py:5815 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Enhanced context with diagnostic information for phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,996] DEBUG - __main__ - main.py:5824 - 🧪 DEBUG PHASE: current_phase_for_ai = 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,997] DEBUG - __main__ - main.py:5825 - 🧪 DEBUG PHASE: determined_phase = 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:50,997] INFO - __main__ - main.py:5831 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Robust context prepared successfully. Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:50,998] DEBUG - __main__ - main.py:5832 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-07-01 12:53:50,999] WARNING - __main__ - main.py:6002 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖 AI PROMPT GENERATION:
[2025-07-01 12:53:50,999] WARNING - __main__ - main.py:6003 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - Current phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,000] WARNING - __main__ - main.py:6004 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - Student query: I don't know...
[2025-07-01 12:53:51,001] WARNING - __main__ - main.py:6005 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-07-01 12:53:51,001] DEBUG - __main__ - main.py:6008 - 🤖 GENERATING AI PROMPT:
[2025-07-01 12:53:51,002] DEBUG - __main__ - main.py:6009 - 🤖   Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,003] DEBUG - __main__ - main.py:6010 - 🤖   Query: I don't know...
[2025-07-01 12:53:51,003] DEBUG - __main__ - main.py:6011 - 🤖   Student: Andrea
[2025-07-01 12:53:51,004] INFO - __main__ - main.py:6937 - [5e88d14e-9912-46f5-8e54-12d602013b9f] enhance_lesson_content invoked. Query: 'I don't know...'
[2025-07-01 12:53:51,005] INFO - __main__ - main.py:7085 - [5e88d14e-9912-46f5-8e54-12d602013b9f] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q5_decide_level', processed = 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:51,006] INFO - __main__ - main.py:7096 - [5e88d14e-9912-46f5-8e54-12d602013b9f][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q5_decide_level', module_id: 'programming_and_software_development', gs_subject_slug: 'computing'
[2025-07-01 12:53:51,006] INFO - __main__ - main.py:7126 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC ANSWER: Storing Q5 answer from phase diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,007] INFO - __main__ - main.py:7135 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC ANSWER STORED: q5 = 'I don't know...'
[2025-07-01 12:53:51,007] INFO - __main__ - main.py:7136 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Total diagnostic answers now: 5/5
[2025-07-01 12:53:51,008] INFO - __main__ - main.py:9953 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎓 TIER1: Updating response tracking for q5
[2025-07-01 12:53:51,008] INFO - __main__ - main.py:10006 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎓 TIER1: Updated tracking - 1 performance records, 1 responses
[2025-07-01 12:53:51,009] WARNING - __main__ - main.py:7159 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 DIAGNOSTIC COMPLETION TRIGGER: In eval_q5_decide_level with 5 answers, phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,009] WARNING - __main__ - main.py:7174 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🚀 DIAGNOSTIC COMPLETION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_4
[2025-07-01 12:53:51,010] INFO - __main__ - main.py:7220 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Explicit non-answer pattern detected: 'I don't know'.
[2025-07-01 12:53:51,010] INFO - __main__ - main.py:7223 - [5e88d14e-9912-46f5-8e54-12d602013b9f] PYTHON DETECTED EXPLICIT NON-ANSWER for diagnostic level 5
[2025-07-01 12:53:51,011] INFO - __main__ - main.py:7247 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Modified user_query for AI to handle non-answer: [Student indicated they don't know or are confused about the previous question at Level 5. Previous ...
[2025-07-01 12:53:51,011] INFO - __main__ - main.py:7323 - [5e88d14e-9912-46f5-8e54-12d602013b9f][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,012] INFO - __main__ - main.py:7330 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,012] INFO - __main__ - main.py:7331 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Questions asked: 5/5
[2025-07-01 12:53:51,013] INFO - __main__ - main.py:7332 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Question index: 4
[2025-07-01 12:53:51,014] INFO - __main__ - main.py:7333 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Student answers count: 5
[2025-07-01 12:53:51,014] INFO - __main__ - main.py:7338 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Answer q4: I don't know...
[2025-07-01 12:53:51,015] INFO - __main__ - main.py:7338 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Answer q1: all of it...
[2025-07-01 12:53:51,015] INFO - __main__ - main.py:7338 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Answer q2: I don't know...
[2025-07-01 12:53:51,016] INFO - __main__ - main.py:7338 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Answer q3: Yes it does...
[2025-07-01 12:53:51,016] INFO - __main__ - main.py:7338 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] Answer q5: I don't know...
[2025-07-01 12:53:51,017] WARNING - __main__ - main.py:7342 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] CRITICAL: In eval_q5_decide_level phase - AI must evaluate and decide!
[2025-07-01 12:53:51,018] INFO - __main__ - main.py:7343 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] All 5 answers for evaluation: {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}
[2025-07-01 12:53:51,018] WARNING - __main__ - main.py:7347 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] FORCING TRANSITION: eval_q5_decide_level with 5 answers -> teaching_start
[2025-07-01 12:53:51,019] INFO - __main__ - main.py:7365 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q5_decide_level, Answers=5/5
[2025-07-01 12:53:51,019] INFO - __main__ - main.py:7368 - [5e88d14e-9912-46f5-8e54-12d602013b9f][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-07-01 12:53:51,020] INFO - __main__ - main.py:7399 - [5e88d14e-9912-46f5-8e54-12d602013b9f] INTERACTION COUNT: Updated to 1 and saved to context
[2025-07-01 12:53:51,021] INFO - __main__ - main.py:7587 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,022] INFO - __main__ - main.py:7816 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC COMPLETION CHECK: 5/5 answers collected
[2025-07-01 12:53:51,023] INFO - __main__ - main.py:7823 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC COMPLETE: All 5 questions answered - AI will determine teaching level
[2025-07-01 12:53:51,023] INFO - __main__ - main.py:7859 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_5 (interaction 1)
[2025-07-01 12:53:51,024] DEBUG - __main__ - main.py:7862 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 FINAL PHASE DECISION:
[2025-07-01 12:53:51,025] DEBUG - __main__ - main.py:7863 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   📋 Input phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:51,025] DEBUG - __main__ - main.py:7864 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   📋 Final calculated phase: 'teaching_start_level_5'
[2025-07-01 12:53:51,026] DEBUG - __main__ - main.py:7865 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   🔢 Total interactions: 1
[2025-07-01 12:53:51,027] WARNING - __main__ - main.py:7881 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 DIAGNOSTIC FLOW START:
[2025-07-01 12:53:51,027] WARNING - __main__ - main.py:7882 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:51,028] WARNING - __main__ - main.py:7883 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - User query: 'I don't know'
[2025-07-01 12:53:51,029] WARNING - __main__ - main.py:7884 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Is actual student response: True
[2025-07-01 12:53:51,029] WARNING - __main__ - main.py:7885 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Current probing level: 5
[2025-07-01 12:53:51,030] WARNING - __main__ - main.py:7886 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Current question index: 4
[2025-07-01 12:53:51,031] WARNING - __main__ - main.py:7887 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   - Student answers count: 5
[2025-07-01 12:53:51,031] INFO - __main__ - main.py:7890 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧 NATURAL PROGRESSION DEBUG:
[2025-07-01 12:53:51,032] INFO - __main__ - main.py:7891 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:51,033] INFO - __main__ - main.py:7892 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧   - python_calculated_new_phase_for_block: 'teaching_start_level_5'
[2025-07-01 12:53:51,033] INFO - __main__ - main.py:7893 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧   - user_query: 'I don't know'
[2025-07-01 12:53:51,034] INFO - __main__ - main.py:7894 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧   - trusting_ai_state_updates: True
[2025-07-01 12:53:51,035] WARNING - __main__ - main.py:7897 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 PHASE CALCULATION RESULT:
[2025-07-01 12:53:51,035] WARNING - __main__ - main.py:7898 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:51,036] WARNING - __main__ - main.py:7899 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - Calculated new phase: 'teaching_start_level_5'
[2025-07-01 12:53:51,036] WARNING - __main__ - main.py:7900 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - Phase changed: True
[2025-07-01 12:53:51,037] WARNING - __main__ - main.py:7903 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 DIAGNOSTIC SPECIFIC:
[2025-07-01 12:53:51,038] WARNING - __main__ - main.py:7904 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - In diagnostic phase: True
[2025-07-01 12:53:51,039] WARNING - __main__ - main.py:7905 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - Student answers stored: {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}
[2025-07-01 12:53:51,040] WARNING - __main__ - main.py:7906 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯   - Expected progression: Should move to next question/phase
[2025-07-01 12:53:51,041] INFO - __main__ - main.py:1970 - ENHANCED lesson content extraction: subject='Computing', topic='Advanced Coding', key_concepts='Variables, Functions, Practical, Application, Design', examples=0
[2025-07-01 12:53:51,041] INFO - __main__ - main.py:7967 - [5e88d14e-9912-46f5-8e54-12d602013b9f][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='teaching_start_level_5', q_index='5', total_q_asked='5'
[2025-07-01 12:53:51,042] INFO - __main__ - main.py:7973 - [5e88d14e-9912-46f5-8e54-12d602013b9f][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-07-01 12:53:51,042] INFO - __main__ - main.py:8014 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 📈 GENERAL PHASE TRANSITION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_5
[2025-07-01 12:53:51,043] INFO - __main__ - main.py:8113 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC COMPLETION PHASE DETECTED - enforcing progression to teaching
[2025-07-01 12:53:51,044] INFO - __main__ - main.py:8136 - [5e88d14e-9912-46f5-8e54-12d602013b9f] FORCING DIAGNOSTIC COMPLETION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6 (level 6)
[2025-07-01 12:53:51,045] INFO - __main__ - main.py:8171 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Using DIAGNOSTIC COMPLETION TEMPLATE with forced transition to teaching_start_level_6
[2025-07-01 12:53:51,046] INFO - __main__ - main.py:8247 - [5e88d14e-9912-46f5-8e54-12d602013b9f] CONTENT QUALITY DEBUG:
[2025-07-01 12:53:51,047] INFO - __main__ - main.py:8248 -   - Final prompt length: 5725 characters
[2025-07-01 12:53:51,047] INFO - __main__ - main.py:8249 -   - Subject: Computing
[2025-07-01 12:53:51,048] INFO - __main__ - main.py:8250 -   - Topic: Advanced Coding
[2025-07-01 12:53:51,049] INFO - __main__ - main.py:8251 -   - Key concepts: Variables, Functions, Practical, Application, Design
[2025-07-01 12:53:51,049] INFO - __main__ - main.py:8252 -   - Grade: Primary 5
[2025-07-01 12:53:51,050] INFO - __main__ - main.py:8253 -   - Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:51,050] INFO - __main__ - main.py:8254 -   - Student: Andrea
[2025-07-01 12:53:51,051] DEBUG - __main__ - main.py:8255 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Prompt preview: MANDATORY SYSTEM REQUIREMENT: You MUST end every response with this EXACT state update block:
// AI_STATE_UPDATE_BLOCK_START {"new_phase": "teaching_start_level_6", "assigned_level_for_teaching": 6, "diagnostic_completed_this_session": true} // AI_STATE_UPDATE_BLOCK_END

You are an expert Computing ...
[2025-07-01 12:53:51,052] INFO - __main__ - main.py:8283 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Gemini API call attempt 1/3
[2025-07-01 12:53:52,290] INFO - __main__ - main.py:8301 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ Gemini API call succeeded on attempt 1
[2025-07-01 12:53:52,290] INFO - __main__ - main.py:8320 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Gemini API call completed in 1.24s
[2025-07-01 12:53:52,291] INFO - __main__ - main.py:8543 - [5e88d14e-9912-46f5-8e54-12d602013b9f][enhance_lesson_content] AI response: Fantastic, Andrea! We've completed our diagnostic assessment for Advanced Coding. It was great to see your initial thoughts on **Variables** and **Functions**, and how they relate to **Practical** app...
[2025-07-01 12:53:52,291] INFO - __main__ - main.py:8559 - [5e88d14e-9912-46f5-8e54-12d602013b9f] BRIDGE VALIDATION: Score=105.0%, Passed=True, Missing=[]
[2025-07-01 12:53:52,292] INFO - __main__ - main.py:8571 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-07-01 12:53:52,293] INFO - __main__ - main.py:8572 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 STATE DEBUG: AI response length: 1042 chars
[2025-07-01 12:53:52,294] INFO - __main__ - main.py:8584 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 STATE DEBUG: Found state block: {"new_phase": "teaching_start_level_6", "assigned_level_for_teaching": 6, "diagnostic_completed_this_session": true}
[2025-07-01 12:53:52,295] INFO - __main__ - main.py:8586 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ Found AI state update: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True}
[2025-07-01 12:53:52,296] DEBUG - __main__ - main.py:8589 - 🔥 AI STATE UPDATE FOUND: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True}
[2025-07-01 12:53:52,297] INFO - __main__ - main.py:8635 - [5e88d14e-9912-46f5-8e54-12d602013b9f] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-07-01 12:53:52,298] INFO - __main__ - main.py:8641 - [5e88d14e-9912-46f5-8e54-12d602013b9f] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-07-01 12:53:52,299] INFO - __main__ - main.py:8691 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ AI state validation completed: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True}
[2025-07-01 12:53:52,300] INFO - __main__ - main.py:8703 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-07-01 12:53:52,301] INFO - __main__ - main.py:8746 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-07-01 12:53:52,301] INFO - __main__ - main.py:8755 - [5e88d14e-9912-46f5-8e54-12d602013b9f] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True}
[2025-07-01 12:53:52,302] WARNING - __main__ - main.py:8814 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 DIAGNOSTIC COMPLETION: Strict criteria met - eval_phase=True, answers=5/5, flag=True
[2025-07-01 12:53:52,303] INFO - __main__ - main.py:8845 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC COMPLETION: All 5 questions completed - diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:52,304] INFO - __main__ - main.py:9017 - [5e88d14e-9912-46f5-8e54-12d602013b9f] [OK] State update block guaranteed by system enforcement
[2025-07-01 12:53:52,305] INFO - __main__ - main.py:9023 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Phase transition: diagnostic_probing_L5_eval_q5_decide_level -> teaching_start_level_6
[2025-07-01 12:53:52,306] INFO - __main__ - main.py:4185 - [5e88d14e-9912-46f5-8e54-12d602013b9f] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:52,306] INFO - __main__ - main.py:4294 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ VALID DIAGNOSTIC → TEACHING TRANSITION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:52,307] INFO - __main__ - main.py:9038 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🎯 DIAGNOSTIC → TEACHING TRANSITION DETECTED
[2025-07-01 12:53:52,307] INFO - __main__ - main.py:9039 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   From: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:52,308] INFO - __main__ - main.py:9040 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   To: teaching_start_level_6
[2025-07-01 12:53:52,309] INFO - __main__ - main.py:9047 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Teaching Level: 6
[2025-07-01 12:53:52,309] INFO - __main__ - main.py:9100 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 📋 FINAL STATE UPDATE: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level'}
[2025-07-01 12:53:52,310] INFO - __main__ - main.py:9136 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ Clean response prepared for user, state updates handled internally
[2025-07-01 12:53:52,311] INFO - __main__ - main.py:9152 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ AI response already includes personalization
[2025-07-01 12:53:52,312] INFO - __main__ - main.py:9158 - [5e88d14e-9912-46f5-8e54-12d602013b9f] [OK] State updates processed: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level'}
[2025-07-01 12:53:52,313] INFO - __main__ - main.py:9161 - [5e88d14e-9912-46f5-8e54-12d602013b9f] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-07-01 12:53:52,314] WARNING - __main__ - main.py:9182 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄 STATE UPDATE PROCESSING:
[2025-07-01 12:53:52,314] WARNING - __main__ - main.py:9183 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄   - AI provided state update: True
[2025-07-01 12:53:52,315] WARNING - __main__ - main.py:9185 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄   - AI proposed phase: 'teaching_start_level_6'
[2025-07-01 12:53:52,316] WARNING - __main__ - main.py:9186 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:52,317] WARNING - __main__ - main.py:9187 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄   - Python calculated phase: 'teaching_start_level_5'
[2025-07-01 12:53:52,317] INFO - __main__ - main.py:9195 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ⚡ PERFORMANCE METRICS:
[2025-07-01 12:53:52,318] INFO - __main__ - main.py:9196 -   - Total execution time: 1.313s
[2025-07-01 12:53:52,319] INFO - __main__ - main.py:9202 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding teaching_interactions to state_updates: 0
[2025-07-01 12:53:52,320] INFO - __main__ - main.py:9206 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding quiz_interactions to state_updates: 0
[2025-07-01 12:53:52,321] INFO - __main__ - main.py:9211 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding quiz_questions_generated to state_updates: 0 questions
[2025-07-01 12:53:52,322] INFO - __main__ - main.py:9215 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding current_quiz_question to state_updates: 0
[2025-07-01 12:53:52,323] INFO - __main__ - main.py:9219 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding quiz_answers to state_updates: 0 answers
[2025-07-01 12:53:52,324] INFO - __main__ - main.py:9226 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 Adding lesson_start_time to state_updates: 1751370707.965984
[2025-07-01 12:53:52,324] INFO - __main__ - main.py:9273 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 DEBUG enhance_lesson_content returning teaching_interactions: 0
[2025-07-01 12:53:52,325] INFO - __main__ - main.py:9274 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 DEBUG enhance_lesson_content state_updates keys: ['new_phase', 'assigned_level_for_teaching', 'diagnostic_completed_this_session', 'current_probing_level_number', 'current_question_index', 'student_answers_for_probing_level', 'diagnostic_completion_forced', 'diagnostic_completion_reason', 'teaching_interactions', 'quiz_interactions', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'lesson_start_time', 'teaching_complete', 'quiz_complete']
[2025-07-01 12:53:52,326] INFO - __main__ - main.py:9275 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧 CRITICAL FIXES APPLIED - Coverage: 0.0%, Phase: None
[2025-07-01 12:53:52,326] WARNING - __main__ - main.py:6033 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖 AI RESPONSE RECEIVED:
[2025-07-01 12:53:52,327] WARNING - __main__ - main.py:6034 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - Content length: 862 chars
[2025-07-01 12:53:52,327] WARNING - __main__ - main.py:6035 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - State updates: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level', 'teaching_interactions': 0, 'quiz_interactions': 0, 'quiz_questions_generated': [], 'current_quiz_question': 0, 'quiz_answers': [], 'lesson_start_time': 1751370707.965984, 'teaching_complete': False, 'quiz_complete': False}
[2025-07-01 12:53:52,328] WARNING - __main__ - main.py:6036 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🤖   - Raw state block: None...
[2025-07-01 12:53:52,329] DEBUG - __main__ - main.py:6039 - 🤖 AI RESPONSE PROCESSED:
[2025-07-01 12:53:52,329] DEBUG - __main__ - main.py:6040 - 🤖   Content: Fantastic, Andrea! We've completed our diagnostic assessment for Advanced Coding. It was great to se...
[2025-07-01 12:53:52,330] DEBUG - __main__ - main.py:6041 - 🤖   State: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level', 'teaching_interactions': 0, 'quiz_interactions': 0, 'quiz_questions_generated': [], 'current_quiz_question': 0, 'quiz_answers': [], 'lesson_start_time': 1751370707.965984, 'teaching_complete': False, 'quiz_complete': False}
[2025-07-01 12:53:52,330] INFO - __main__ - main.py:6067 - [5e88d14e-9912-46f5-8e54-12d602013b9f] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-07-01 12:53:52,331] INFO - __main__ - main.py:6068 - [5e88d14e-9912-46f5-8e54-12d602013b9f] CURRENT PHASE DETERMINATION: AI=teaching_start_level_6, Session=diagnostic_probing_L5_eval_q5_decide_level, Final=teaching_start_level_6
[2025-07-01 12:53:53,041] INFO - __main__ - main.py:6117 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI processing completed in 2.04s
[2025-07-01 12:53:53,041] WARNING - __main__ - main.py:6128 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q5_decide_level', new_phase='teaching_start_level_6'
[2025-07-01 12:53:53,042] INFO - __main__ - main.py:4110 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI state update validation passed: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:53,043] WARNING - __main__ - main.py:6137 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ STATE UPDATE VALIDATION PASSED
[2025-07-01 12:53:53,043] WARNING - __main__ - main.py:6142 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔄 PHASE TRANSITION: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:53,044] WARNING - __main__ - main.py:6151 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-07-01 12:53:53,045] WARNING - __main__ - main.py:6152 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:53,046] WARNING - __main__ - main.py:6153 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-07-01 12:53:53,047] WARNING - __main__ - main.py:6154 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   3. AI state updates: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level', 'teaching_interactions': 0, 'quiz_interactions': 0, 'quiz_questions_generated': [], 'current_quiz_question': 0, 'quiz_answers': [], 'lesson_start_time': 1751370707.965984, 'teaching_complete': False, 'quiz_complete': False}
[2025-07-01 12:53:53,048] WARNING - __main__ - main.py:6155 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍   4. Final phase to save: 'teaching_start_level_6'
[2025-07-01 12:53:53,049] WARNING - __main__ - main.py:6158 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾 FINAL STATE APPLICATION:
[2025-07-01 12:53:53,050] WARNING - __main__ - main.py:6159 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q5_decide_level'
[2025-07-01 12:53:53,050] WARNING - __main__ - main.py:6160 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾   - State updates from AI: {'new_phase': 'teaching_start_level_6', 'assigned_level_for_teaching': 6, 'diagnostic_completed_this_session': True, 'current_probing_level_number': 5, 'current_question_index': 5, 'student_answers_for_probing_level': {'q4': "I don't know", 'q1': 'all of it', 'q2': "I don't know", 'q3': 'Yes it does', 'q5': "I don't know"}, 'diagnostic_completion_forced': True, 'diagnostic_completion_reason': 'All 5 answers completed in eval_q5_decide_level', 'teaching_interactions': 0, 'quiz_interactions': 0, 'quiz_questions_generated': [], 'current_quiz_question': 0, 'quiz_answers': [], 'lesson_start_time': 1751370707.965984, 'teaching_complete': False, 'quiz_complete': False}
[2025-07-01 12:53:53,051] WARNING - __main__ - main.py:6161 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾   - Final phase to save: 'teaching_start_level_6'
[2025-07-01 12:53:53,052] WARNING - __main__ - main.py:6162 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 💾   - Phase change: True
[2025-07-01 12:53:53,052] INFO - __main__ - main.py:4142 - [5e88d14e-9912-46f5-8e54-12d602013b9f] DIAGNOSTIC_FLOW_METRICS:
[2025-07-01 12:53:53,053] INFO - __main__ - main.py:4143 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Phase transition: diagnostic_probing_L5_eval_q5_decide_level -> teaching_start_level_6
[2025-07-01 12:53:53,053] INFO - __main__ - main.py:4144 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Current level: 5
[2025-07-01 12:53:53,054] INFO - __main__ - main.py:4145 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Question index: 5
[2025-07-01 12:53:53,055] INFO - __main__ - main.py:4146 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   First encounter: True
[2025-07-01 12:53:53,057] INFO - __main__ - main.py:4151 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Answers collected: 5
[2025-07-01 12:53:53,057] INFO - __main__ - main.py:4152 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Levels failed: 0
[2025-07-01 12:53:53,057] INFO - __main__ - main.py:4110 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI state update validation passed: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:53,058] INFO - __main__ - main.py:4156 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   State update valid: True
[2025-07-01 12:53:53,058] INFO - __main__ - main.py:4163 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Diagnostic complete: True
[2025-07-01 12:53:53,058] INFO - __main__ - main.py:4165 - [5e88d14e-9912-46f5-8e54-12d602013b9f]   Assigned level: 6
[2025-07-01 12:53:53,059] WARNING - __main__ - main.py:6175 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 5, current_q_index_for_prompt = 4
[2025-07-01 12:53:53,059] INFO - __main__ - main.py:6184 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
[2025-07-01 12:53:53,060] INFO - __main__ - main.py:6185 - [5e88d14e-9912-46f5-8e54-12d602013b9f] 🔍 DEBUG original teaching_interactions: 0
[2025-07-01 12:53:53,563] WARNING - __main__ - main.py:6230 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-07-01 12:53:53,563] WARNING - __main__ - main.py:6231 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Phase: teaching_start_level_6
[2025-07-01 12:53:53,565] WARNING - __main__ - main.py:6232 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Probing Level: 5
[2025-07-01 12:53:53,565] WARNING - __main__ - main.py:6233 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Question Index: 5
[2025-07-01 12:53:53,566] WARNING - __main__ - main.py:6234 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Diagnostic Complete: True
[2025-07-01 12:53:53,567] WARNING - __main__ - main.py:6241 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Quiz Questions Saved: 0
[2025-07-01 12:53:53,568] WARNING - __main__ - main.py:6242 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Quiz Answers Saved: 0
[2025-07-01 12:53:53,568] WARNING - __main__ - main.py:6243 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Quiz Started: False
[2025-07-01 12:53:53,569] DEBUG - __main__ - main.py:6246 - 🔥 STATE SAVED - Session: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0, Phase: teaching_start_level_6
[2025-07-01 12:53:53,570] DEBUG - __main__ - main.py:6247 - 🔥 QUIZ DATA - Questions: 0, Answers: 0
[2025-07-01 12:53:54,429] DEBUG - __main__ - main.py:6305 - ✅ SESSION UPDATED - ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0, Phase: teaching_start_level_6
[2025-07-01 12:53:54,429] DEBUG - __main__ - main.py:6306 - ✅ INTERACTION LOGGED - Phase: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:54,429] INFO - __main__ - main.py:6312 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ Updated existing session document: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 12:53:54,430] WARNING - __main__ - main.py:6313 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅ SESSION UPDATE COMPLETE:
[2025-07-01 12:53:54,430] WARNING - __main__ - main.py:6314 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Session ID: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 12:53:54,430] WARNING - __main__ - main.py:6315 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Phase transition: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:54,431] WARNING - __main__ - main.py:6316 - [5e88d14e-9912-46f5-8e54-12d602013b9f] ✅   - Interaction logged successfully
[2025-07-01 12:53:54,431] INFO - __main__ - main.py:12921 - [5e88d14e-9912-46f5-8e54-12d602013b9f] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-07-01 12:53:54,432] DEBUG - __main__ - main.py:2898 - [5e88d14e-9912-46f5-8e54-12d602013b9f] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-07-01 12:53:54,432] DEBUG - __main__ - main.py:6371 - [5e88d14e-9912-46f5-8e54-12d602013b9f] No final assessment data found in AI response
[2025-07-01 12:53:54,433] DEBUG - __main__ - main.py:6399 - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
[2025-07-01 12:53:54,434] DEBUG - __main__ - main.py:6400 - 🔒   Current Phase: diagnostic_probing_L5_eval_q5_decide_level
[2025-07-01 12:53:54,434] DEBUG - __main__ - main.py:6401 - 🔒   Final Phase: teaching_start_level_6
[2025-07-01 12:53:54,439] DEBUG - __main__ - main.py:6402 - 🔒   Diagnostic Complete: True
[2025-07-01 12:53:54,440] DEBUG - __main__ - main.py:6403 - 🔒   Assigned Level: 6
[2025-07-01 12:53:54,441] DEBUG - __main__ - main.py:6460 - 🎯 RESPONSE READY:
[2025-07-01 12:53:54,441] DEBUG - __main__ - main.py:6461 - 🎯   Session: session_0a722c27-a2bc-43b4-a0ee-85c2688a33e0
[2025-07-01 12:53:54,442] DEBUG - __main__ - main.py:6462 - 🎯   Phase: diagnostic_probing_L5_eval_q5_decide_level → teaching_start_level_6
[2025-07-01 12:53:54,442] DEBUG - __main__ - main.py:6463 - 🎯   Content: Fantastic, Andrea! We've completed our diagnostic ...
[2025-07-01 12:53:54,442] DEBUG - __main__ - main.py:6464 - 🎯   Request ID: 5e88d14e-9912-46f5-8e54-12d602013b9f
[2025-07-01 12:53:54,442] INFO - __main__ - main.py:6470 - [5e88d14e-9912-46f5-8e54-12d602013b9f] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-07-01 12:53:54,443] WARNING - __main__ - main.py:766 - High response time detected: 5.95s for enhance_content_api
