"""
This is a code snippet to be integrated into the enhance_lesson_api function in main.py.
Copy and paste this code at the appropriate location in your function where phase determination happens.
"""

# --- Determine Next Phase to SAVE to Firestore based on AI response & current phase ---
phase_to_save_in_firestore = current_phase_for_ai  # Default to current phase

if ai_response_text:  # Only proceed if there's an AI response
    ai_response_lower = ai_response_text.lower()

    if current_phase_for_ai == LESSON_PHASE_DIAGNOSTIC:
        # --- START DETAILED DEBUGGING FOR DIAGNOSTIC PHASE TRANSITION ---
        logger.info(f"[{request_id}] DIAGNOSTIC_CHECK: AI Response (lower) for phase check (first 250 chars): '{ai_response_lower[:250]}...'")

        is_assigning_level_1_explicitly_after_probes = (
            "we'll start at level 1" in ai_response_lower and
            ("level 1 is a great starting point" in ai_response_lower or "level 1 is the perfect place" in ai_response_lower or "level 1 it is!" in ai_response_lower)
        )
        logger.debug(f"[{request_id}] DIAGNOSTIC_CHECK_FLAG: is_assigning_level_1_explicitly_after_probes = {is_assigning_level_1_explicitly_after_probes}")

        final_level_assignment_phrases = [
            "is our starting point", 
            "is a good starting point", 
            "is a great starting point",
            "is the perfect place for us to start",
            "is the perfect place to start",
            "seems appropriate",
            "would be a good fit",
            "would be appropriate",
            "is where we'll start"
        ]
        cond1_assignment_phrase = any(phrase in ai_response_lower for phrase in final_level_assignment_phrases)
        cond1_level_mentioned = "level " in ai_response_lower  # Checks if "level " (with a space) is present
        is_assigning_any_final_level = cond1_assignment_phrase and cond1_level_mentioned
        
        logger.debug(f"[{request_id}] DIAGNOSTIC_CHECK_FLAG: (cond1_assignment_phrase: {cond1_assignment_phrase}, cond1_level_mentioned: {cond1_level_mentioned}) -> is_assigning_any_final_level = {is_assigning_any_final_level}")

        is_presenting_a_list_of_new_diagnostic_questions = (
            ("here are a few questions" in ai_response_lower or 
             "here are a couple of questions" in ai_response_lower or 
             "here are some questions" in ai_response_lower or
             "let me ask you" in ai_response_lower or
             "let's try another question" in ai_response_lower or
             "let's try a different question" in ai_response_lower)
            # Add more checks if AI uses other phrases for presenting new diagnostic sets
        )
        logger.debug(f"[{request_id}] DIAGNOSTIC_CHECK_FLAG: is_presenting_a_list_of_new_diagnostic_questions = {is_presenting_a_list_of_new_diagnostic_questions}")
        
        is_assigning_any_final_level_and_teaching = is_assigning_any_final_level and not is_presenting_a_list_of_new_diagnostic_questions
        logger.debug(f"[{request_id}] DIAGNOSTIC_CHECK_FLAG: is_assigning_any_final_level_and_teaching = {is_assigning_any_final_level_and_teaching}")
        
        cond3a_explicit_probe_lower_intro = (
            "let's try some slightly different questions" in ai_response_lower or 
            "let's switch gears slightly" in ai_response_lower or 
            "let's try some questions from level" in ai_response_lower or  # Covers "Let's try some questions from Level 4"
            "let's look at some concepts typically covered around level" in ai_response_lower or
            "let's try some questions focused on level" in ai_response_lower or  # Covers "Let's try some questions focused on Level 4 concepts"
            "let's try an easier question" in ai_response_lower or
            "let's try a simpler question" in ai_response_lower or
            "let me ask you an easier question" in ai_response_lower
        )
        # is_presenting_a_list_of_new_diagnostic_questions already checks for "here are a few/couple/some questions"
        is_explicitly_probing_with_new_questions = cond3a_explicit_probe_lower_intro and is_presenting_a_list_of_new_diagnostic_questions
        
        logger.debug(f"[{request_id}] DIAGNOSTIC_CHECK_FLAG: (cond3a_explicit_probe_lower_intro: {cond3a_explicit_probe_lower_intro}) -> is_explicitly_probing_with_new_questions = {is_explicitly_probing_with_new_questions}")
        # --- END DETAILED DEBUGGING ---

        # Check for "I don't know" or similar vague responses that might cause the system to get stuck
        student_message_lower = context.get('user_message', '').lower() if context.get('user_message') else ''
        is_user_response_vague = any(phrase in student_message_lower for phrase in [
            "i don't know", "don't know", "not sure", "no idea", "idk", "i'm not sure", 
            "i am not sure", "i'm confused", "i am confused", "i don't understand",
            "i do not know", "i do not understand", "no clue", "confused", "uncertain",
            "have no idea", "can't tell", "cannot tell"
        ])
        
        # Check if AI is repeating the same question
        current_question_index = session_state_data.get('current_question_index', 0)
        previous_question_index = session_state_data.get('previous_question_index', -1)
        is_repeating_question = current_question_index == previous_question_index and current_question_index > 0
        
        # Update question index tracking in the context
        context['previous_question_index'] = current_question_index
        
        # Get the count of consecutive diagnostic turns
        consecutive_diagnostic_turns = session_state_data.get('diagnostic_turns_count', 0)
        consecutive_vague_responses = session_state_data.get('vague_response_count', 0)
        
        # Update counters based on the current interaction
        if is_user_response_vague:
            consecutive_vague_responses += 1
        else:
            consecutive_vague_responses = 0
            
        consecutive_diagnostic_turns += 1
        
        # Update the session state with the new counters
        try:
            updates = {
                'diagnostic_turns_count': consecutive_diagnostic_turns,
                'vague_response_count': consecutive_vague_responses,
                'previous_question_index': current_question_index,
                'updated_at': firestore.SERVER_TIMESTAMP
            }
            
            if is_user_response_vague:
                # Also record the most recent vague response for context
                updates['last_vague_response'] = student_message_lower
            
            session_state_ref.update(updates)
            logger.debug(f"[{request_id}] Updated diagnostic counters: turns={consecutive_diagnostic_turns}, vague={consecutive_vague_responses}")
        except Exception as e:
            logger.error(f"[{request_id}] Error updating diagnostic counters: {str(e)}")
        
        # Decision logic for phase transitions
        if is_assigning_level_1_explicitly_after_probes: 
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            logger.info(f"[{request_id}] Diagnostic complete. AI assigned Level 1 explicitly after probes. Transitioning to TEACHING.")
        elif is_assigning_any_final_level_and_teaching: 
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            logger.info(f"[{request_id}] Diagnostic complete. AI assigned a final level (not necessarily L1) and is teaching. Transitioning to TEACHING.")
        elif is_explicitly_probing_with_new_questions: 
            phase_to_save_in_firestore = LESSON_PHASE_DIAGNOSTIC
            logger.info(f"[{request_id}] Diagnostic ongoing. AI is explicitly probing with new questions. Remaining in DIAGNOSTIC.")
        else:
            # Handle specific edge cases that might cause the system to get stuck
            if is_user_response_vague and consecutive_vague_responses >= 2:
                # If student has given multiple vague responses, force a level change
                current_probing_level = session_state_data.get('current_probing_level_number', 2)
                new_probing_level = max(1, current_probing_level - 1)  # Move to an easier level
                
                # Update the session state with the new level
                try:
                    session_state_ref.update({
                        'current_probing_level_number': new_probing_level,
                        'updated_at': firestore.SERVER_TIMESTAMP,
                        'vague_response_level_change': True,
                        'level_changed_from': current_probing_level,
                        'level_changed_to': new_probing_level,
                        'level_change_reason': 'multiple_vague_responses'
                    })
                    logger.info(f"[{request_id}] Student gave multiple vague responses. Moving from level {current_probing_level} to {new_probing_level}.")
                    
                    # Add a note to the context to inform the AI
                    context['level_change_note'] = f"Student gave multiple vague responses. Moving from level {current_probing_level} to {new_probing_level}."
                except Exception as e:
                    logger.error(f"[{request_id}] Error updating probing level after vague responses: {str(e)}")
                
                # Stay in diagnostic phase but with the new level
                phase_to_save_in_firestore = LESSON_PHASE_DIAGNOSTIC
            
            # CRITICAL FIX: Check for 5 diagnostic answers and force transition to teaching
            elif session_state_data.get('student_answers_for_probing_level'):
                num_diagnostic_answers = len(session_state_data.get('student_answers_for_probing_level', {}))

                if num_diagnostic_answers >= 5:
                    phase_to_save_in_firestore = LESSON_PHASE_TEACHING

                    # Calculate teaching level based on diagnostic performance
                    answers = session_state_data.get('student_answers_for_probing_level', {})

                    # CRITICAL FIX: Proper diagnostic scoring that handles "I don't know" responses
                    substantial_answers = 0
                    for answer in answers.values():
                        answer_text = str(answer).strip().lower()

                        # Check if answer is substantial and not a "don't know" response
                        if len(answer_text) > 10:  # Minimum length check
                            # Check for "I don't know" type responses
                            dont_know_phrases = [
                                "i don't know", "don't know", "not sure", "no idea", "idk",
                                "i'm not sure", "i am not sure", "i'm confused", "i am confused",
                                "i don't understand", "i do not know", "i do not understand",
                                "no clue", "confused", "uncertain", "have no idea", "can't tell",
                                "cannot tell", "not really", "maybe", "i think", "i guess"
                            ]

                            # Only count as substantial if it's not a "don't know" response
                            is_dont_know = any(phrase in answer_text for phrase in dont_know_phrases)
                            if not is_dont_know and len(answer_text.split()) >= 3:  # At least 3 words
                                substantial_answers += 1

                    current_probing_level = session_state_data.get('current_probing_level_number', 5)

                    logger.info(f"[{request_id}] DIAGNOSTIC SCORING: {substantial_answers}/5 substantial answers from {len(answers)} total answers")
                    logger.info(f"[{request_id}] Student answers: {answers}")

                    # Apply CORRECT diagnostic scoring logic
                    if substantial_answers >= 5:  # 5/5 substantial answers = move up
                        assigned_level = min(10, current_probing_level + 1)
                        logger.info(f"[{request_id}] EXCELLENT: {substantial_answers}/5 substantial answers - moving UP to level {assigned_level}")
                    elif substantial_answers >= 4:  # 4/5 substantial answers = stay same
                        assigned_level = current_probing_level
                        logger.info(f"[{request_id}] GOOD: {substantial_answers}/5 substantial answers - staying at level {assigned_level}")
                    elif substantial_answers >= 2:  # 2-3/5 substantial answers = stay same
                        assigned_level = current_probing_level
                        logger.info(f"[{request_id}] FAIR: {substantial_answers}/5 substantial answers - staying at level {assigned_level}")
                    else:  # 0-1/5 substantial answers = move down
                        assigned_level = max(1, current_probing_level - 1)
                        logger.info(f"[{request_id}] POOR: {substantial_answers}/5 substantial answers - moving DOWN to level {assigned_level}")

                    # Update the session state with the teaching level
                    try:
                        session_state_ref.update({
                            'assigned_level_for_teaching': assigned_level,
                            'diagnostic_completed_this_session': True,
                            'teaching_level_assigned_reason': 'diagnostic_completed_5_questions',
                            'diagnostic_score': f"{substantial_answers}/5",
                            'updated_at': firestore.SERVER_TIMESTAMP
                        })
                        logger.info(f"[{request_id}] DIAGNOSTIC COMPLETE: Transitioning to TEACHING with level {assigned_level} after {num_diagnostic_answers} answers.")

                        # Add a note to inform the AI
                        context['diagnostic_completion_note'] = f"Diagnostic completed with {num_diagnostic_answers} answers. Moving to teaching mode with level {assigned_level}."
                    except Exception as e:
                        logger.error(f"[{request_id}] Error assigning teaching level after diagnostic completion: {str(e)}")

                else:
                    # Continue diagnostic - not enough answers yet
                    phase_to_save_in_firestore = LESSON_PHASE_DIAGNOSTIC
                    logger.info(f"[{request_id}] Diagnostic ongoing: {num_diagnostic_answers}/5 answers collected. Remaining in DIAGNOSTIC phase.")

            # Force transition to teaching if we've been in diagnostic too long
            elif consecutive_diagnostic_turns > 10 or is_repeating_question:
                phase_to_save_in_firestore = LESSON_PHASE_TEACHING

                # Determine an appropriate teaching level based on diagnostic history
                current_probing_level = session_state_data.get('current_probing_level_number', 1)
                assigned_level = max(1, current_probing_level - 1)  # Default to one level lower than current probing

                # Update the session state with the teaching level
                try:
                    session_state_ref.update({
                        'assigned_level_for_teaching': assigned_level,
                        'teaching_level_assigned_reason': 'diagnostic_timeout_or_repetition',
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                    logger.info(f"[{request_id}] Forcing transition to TEACHING with level {assigned_level} after {consecutive_diagnostic_turns} turns or repeated questions.")

                    # Add a note to inform the AI
                    context['diagnostic_escape_note'] = f"Moving to teaching mode with level {assigned_level} after extended diagnostic phase or question repetition."
                except Exception as e:
                    logger.error(f"[{request_id}] Error assigning teaching level after diagnostic timeout: {str(e)}")
            else:
                # Default case - remain in diagnostic phase
                phase_to_save_in_firestore = LESSON_PHASE_DIAGNOSTIC
                logger.info(f"[{request_id}] Diagnostic ongoing. Remaining in DIAGNOSTIC phase (default case).")
                
        # Additional phase transitions can be added here for other phases if needed
        # This handles cases where the AI assigns a level and its teaching question is subtle.
        elif is_assigning_any_final_level:
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            # Try to extract the assigned level for logging more accurately
            try:
                # Regex to find "level X" where X is one or more digits, and it's followed by a "starting point" phrase
                level_match = re.search(r"level\s*(\d+)\b.*(?:is our starting point|is a good starting point|is a great starting point|is the perfect place for us to start|is the perfect place to start)", ai_response_lower)
                assigned_level_log = level_match.group(1) if level_match else "Unknown (from fallback)"
            except:
                assigned_level_log = "Unknown (parse error)"
            logger.info(f"[{request_id}] Diagnostic (Fallback): AI assigned Level '{assigned_level_log}' with a starting point phrase. Transitioning to TEACHING.")
        else:
            # Stay in diagnostic but increment counter
            phase_to_save_in_firestore = LESSON_PHASE_DIAGNOSTIC
            logger.info(f"[{request_id}] Diagnostic ongoing (turn {consecutive_diagnostic_turns+1}). Remaining in DIAGNOSTIC. AI response (first 100): '{ai_response_text[:100]}...'")
    
    elif current_phase_for_ai == LESSON_PHASE_TEACHING_START:
        # ... (your existing logic for TEACHING_START to TEACHING) ...
        phase_to_save_in_firestore = LESSON_PHASE_TEACHING
        logger.info(f"[{request_id}] From TEACHING_START, transitioning to TEACHING.")
    # ... (add elif for other phases like TEACHING, QUIZ_INITIATE, etc.) ...
    # Make sure to include all your other phase transition logic here:
    elif current_phase_for_ai == LESSON_PHASE_TEACHING:
        # Check for teaching completion phrases
        teaching_complete_phrases = [
            "that concludes our lesson",
            "we've covered all the material",
            "let's move on to the quiz",
            "time for the quiz",
            "let's review what we've learned with a quiz",
            "now that we've covered the concepts",
            "to test your understanding",
            "ready for a quick quiz",
            "let's test your knowledge"
        ]
        
        # Check if teaching is complete and we should move to quiz
        if any(phrase in ai_response_lower for phrase in teaching_complete_phrases):
            phase_to_save_in_firestore = LESSON_PHASE_QUIZ_INITIATE
            logger.info(f"[{request_id}] Teaching content complete. Transitioning to QUIZ_INITIATE.")
            
            # Initialize quiz state in the session
            try:
                session_state_ref.update({
                    'quiz_state': {
                        'current_question': 0,
                        'score': 0,
                        'answers': [],
                        'started_at': firestore.SERVER_TIMESTAMP,
                        'questions': []  # Will be populated by the AI
                    },
                    'updated_at': firestore.SERVER_TIMESTAMP
                })
                logger.info(f"[{request_id}] Initialized quiz state in session.")
            except Exception as e:
                logger.error(f"[{request_id}] Error initializing quiz state: {str(e)}")
        else:
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            logger.info(f"[{request_id}] Continuing in TEACHING phase.")

    # Handle QUIZ_INITIATE phase
    elif current_phase_for_ai == LESSON_PHASE_QUIZ_INITIATE:
        # Check if student has confirmed starting the quiz
        if any(phrase in ai_response_lower for phrase in ["yes", "sure", "ready", "let's start", "go ahead"]):
            phase_to_save_in_firestore = LESSON_PHASE_QUIZ_QUESTIONS
            logger.info(f"[{request_id}] Student confirmed quiz start. Transitioning to QUIZ_QUESTIONS.")
            
            # Generate quiz questions if not already done
            try:
                from quiz_generator import QuizGenerator
                from quiz_manager import QuizManager
                
                # Get lesson content for quiz generation
                lesson_content = context.get('lesson_content', {})
                quiz_generator = QuizGenerator()
                quizzes = quiz_generator.generate_quizzes(
                    lesson_data=lesson_content,
                    metadata={
                        'subject': context.get('subject', ''),
                        'grade': context.get('grade', ''),
                        'lessonRef': context.get('lesson_id', '')
                    }
                )
                
                # Initialize quiz manager with generated questions
                quiz_manager = QuizManager(session_state_ref.get().to_dict() or {})
                quiz_manager.initialize_quiz(quizzes)
                
                logger.info(f"[{request_id}] Generated and initialized quiz with questions.")
            except Exception as e:
                logger.error(f"[{request_id}] Error generating quiz questions: {str(e)}")
        else:
            # If student is not ready, return to teaching
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            logger.info(f"[{request_id}] Student not ready for quiz. Returning to TEACHING.")
            
            # Clear quiz state
            try:
                session_state_ref.update({
                    'quiz_state': firestore.DELETE_FIELD,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })
            except Exception as e:
                logger.error(f"[{request_id}] Error clearing quiz state: {str(e)}")

    # Handle QUIZ_QUESTIONS phase
    elif current_phase_for_ai == LESSON_PHASE_QUIZ_QUESTIONS:
        from quiz_manager import QuizManager
        
        try:
            # Get current session state
            session_data = session_state_ref.get().to_dict() or {}
            quiz_manager = QuizManager(session_data)
            
            # Check if this is the first question
            if not session_data.get('quiz_state', {}).get('questions'):
                logger.info(f"[{request_id}] No questions found in quiz state. Returning to TEACHING.")
                phase_to_save_in_firestore = LESSON_PHASE_TEACHING
            else:
                # Process the answer if this isn't the first question
                current_question = quiz_manager.get_current_question()
                if current_question and 'user_answer' in context:
                    is_correct, next_question = quiz_manager.submit_answer({
                        'answer': context['user_answer'],
                        'question_id': current_question.get('id')
                    })
                    
                    # Update session with answer
                    session_state_ref.update({
                        'quiz_state': quiz_manager.quiz_state,
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                    
                    # Check if quiz is complete
                    if quiz_manager.quiz_state.get('completed', False):
                        phase_to_save_in_firestore = LESSON_PHASE_QUIZ_RESULTS
                        logger.info(f"[{request_id}] Quiz completed. Transitioning to QUIZ_RESULTS.")
                    else:
                        phase_to_save_in_firestore = LESSON_PHASE_QUIZ_QUESTIONS
                        logger.debug(f"[{request_id}] Moving to next question.")
                else:
                    # Still in the middle of the quiz
                    phase_to_save_in_firestore = LESSON_PHASE_QUIZ_QUESTIONS
                    logger.debug(f"[{request_id}] Continuing quiz questions.")
                    
        except Exception as e:
            logger.error(f"[{request_id}] Error processing quiz question: {str(e)}")
            phase_to_save_in_firestore = LESSON_PHASE_TEACHING  # Fallback to teaching on error

    # Handle QUIZ_RESULTS phase
    elif current_phase_for_ai == LESSON_PHASE_QUIZ_RESULTS:
        from quiz_manager import QuizManager
        
        try:
            # Get quiz results
            session_data = session_state_ref.get().to_dict() or {}
            quiz_manager = QuizManager(session_data)
            results = quiz_manager.get_results()
            
            # If we have results and the AI is showing them, move to summary
            if results.get('completed', False) and any(phrase in ai_response_lower 
                                                     for phrase in ["here's how you did", "quiz results", "your score is"]):
                phase_to_save_in_firestore = LESSON_PHASE_CONCLUSION_SUMMARY
                logger.info(f"[{request_id}] Quiz results shown. Transitioning to CONCLUSION_SUMMARY.")
                
                # Prepare assessment data
                assessment_data = {
                    'score': results.get('score', 0),
                    'total_questions': results.get('total_questions', 1),
                    'percentage': results.get('percentage', 0),
                    'started_at': results.get('started_at'),
                    'completed_at': results.get('completed_at')
                }
                
                # Save assessment data
                try:
                    save_student_topic_performance(
                        student_id=student_id,
                        subject_id=subject,
                        grade_context=grade,
                        topic_key=topic_key_for_history,
                        ai_assessment_outcome={
                            'assessed_level_1_to_10': min(10, max(1, int(results.get('percentage', 0) / 10))),  # Convert % to 1-10
                            'quiz_results': assessment_data,
                            'timestamp': firestore.SERVER_TIMESTAMP
                        }
                    )
                    logger.info(f"[{request_id}] Saved assessment data for student {student_id}")
                except Exception as e:
                    logger.error(f"[{request_id}] Error saving assessment data: {str(e)}")
                    
                # Clear quiz state after saving results
                try:
                    session_state_ref.update({
                        'quiz_state': firestore.DELETE_FIELD,
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                except Exception as e:
                    logger.error(f"[{request_id}] Error clearing quiz state: {str(e)}")
            else:
                phase_to_save_in_firestore = LESSON_PHASE_QUIZ_RESULTS
                logger.debug(f"[{request_id}] Processing quiz results.")
                
        except Exception as e:
            logger.error(f"[{request_id}] Error processing quiz results: {str(e)}")
            phase_to_save_in_firestore = LESSON_PHASE_CONCLUSION_SUMMARY  # Move to summary on error
    
    elif current_phase_for_ai == LESSON_PHASE_CONCLUSION_SUMMARY:
        has_assessment_block = "// AI_PERFORMANCE_ASSESSMENT_BLOCK_START" in ai_response_text
        has_comprehensive_summary = any(phrase in ai_response_lower for phrase in [
            "learning objectives review", "achievement assessment", "quiz results integration",
            "key concepts mastery summary", "learning journey progress", "our learning objectives were"
        ])
        
        if has_assessment_block:
            # Try to extract and validate assessment data
            try:
                from phase_transition_validation import validate_phase_transition
                assessment_block_data = extract_ai_assessment_and_homework_data(ai_response_text)
                
                # Validate transition to completed phase
                is_valid, reason = validate_phase_transition(
                    current_phase=current_phase_for_ai,
                    new_phase=LESSON_PHASE_COMPLETED,
                    has_assessment_block=True,
                    assessment_data=assessment_block_data,
                    request_id=request_id
                )
                
                if is_valid:
                    phase_to_save_in_firestore = LESSON_PHASE_COMPLETED
                    logger.info(f"[{request_id}] Conclusion summary complete AND valid assessment block found. Transitioning to COMPLETED. Reason: {reason}")
                else:
                    phase_to_save_in_firestore = LESSON_PHASE_CONCLUSION_SUMMARY
                    logger.warning(f"[{request_id}] Cannot transition to COMPLETED. Remaining in CONCLUSION_SUMMARY. Reason: {reason}")
            except (ImportError, Exception) as e:
                # Fallback to simpler validation if there's an error
                if has_assessment_block and assessment_block_data and assessment_block_data.get("assessment"):
                    phase_to_save_in_firestore = LESSON_PHASE_COMPLETED
                    logger.info(f"[{request_id}] Conclusion summary complete AND assessment block found (fallback validation). Transitioning to COMPLETED.")
                else:
                    phase_to_save_in_firestore = LESSON_PHASE_CONCLUSION_SUMMARY
                    logger.warning(f"[{request_id}] AI indicated conclusion, but assessment block missing or invalid. Remaining in CONCLUSION_SUMMARY.")
                    
                logger.error(f"[{request_id}] Error during phase validation: {str(e)}")
        else:
            logger.info(f"[{request_id}] In CONCLUSION_SUMMARY, no assessment block yet. Remaining.")
            phase_to_save_in_firestore = LESSON_PHASE_CONCLUSION_SUMMARY


# --- Log and Update Firestore ---
logger.info(f"[{request_id}] Phase used by AI for this turn: '{current_phase_for_ai}'. Calculated next phase to save: '{phase_to_save_in_firestore}'.")
# ... (rest of your Firestore update logic for session_state_ref) ...
